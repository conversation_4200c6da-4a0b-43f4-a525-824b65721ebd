{"process": {"version": "Process Supported & Format version", "process": [{"process": "CopyIntoOrient", "version": "process version", "schedule": {}, "invoke": [{"version": "plugin version", "type": "pipeline", "plugin": "sansa", "pipeline": "CopyIntoOrient", "extract": [], "persist": ["1"]}]}, {"process": "PopulateDLA", "version": "process version", "schedule": {}, "invoke": [{"version": "plugin version", "type": "pipeline", "plugin": "sansa", "pipeline": "PopulateDLA", "extract": [], "persist": []}]}, {"process": "PopulateDeltaDLA", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 0, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "PopulateDeltaDLA", "extract": [], "persist": []}]}, {"process": "DetectExpiredDLAItems", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 0, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "DetectExpiredDLAItems", "extract": [], "persist": []}]}, {"process": "JCLStaticScanner", "version": "process version", "schedule": {}, "invoke": [{"version": "plugin version", "type": "pipeline", "plugin": "jcl", "pipeline": "StaticScanner", "extract": [], "persist": []}]}, {"process": "APIDataTransfer", "version": "process version", "schedule": {}, "invoke": [{"version": "plugin version", "type": "pipeline", "plugin": "jcl", "pipeline": "APIDataTransfer", "extract": [], "persist": []}]}, {"process": "FileDataTransfer", "version": "process version", "schedule": {}, "invoke": [{"version": "plugin version", "type": "pipeline", "plugin": "jcl", "pipeline": "FileDataTransfer", "extract": [], "persist": []}]}, {"process": "JCLOperationDataGenerate", "version": "process version", "schedule": {}, "invoke": [{"version": "plugin version", "type": "pipeline", "plugin": "jcl", "pipeline": "JCLOperationDataGenerate", "extract": [], "persist": []}]}]}, "extract": {"version": "Data Source Supported & Format Version", "extract": []}, "persist": {"version": "Data Persist Supported & Format Version", "persist": [{"type": "Graph", "id": "0", "version": "****", "timestamp": 0, "url": "remote:zrdds-orientdb/Demo", "user": "admin", "password": "admin"}, {"type": "Graph", "id": "1", "version": "****", "timestamp": 0, "url": "remote:zrdds-orientdb/Demo", "user": "root", "password": "rootpwd"}]}}