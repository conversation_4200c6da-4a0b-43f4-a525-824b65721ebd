services:
  kafkabridge:
    build:
      context: ..
      dockerfile: zrdds-zoa-support/kafka/kafkabridge/Dockerfile
      args:
        REPO: ${zrdds_zoa_support_repo:-na}
        COMMIT: ${zrdds_zoa_support_commit:-na}
    image: icr.io/zoa-oci/zrdds-kafkabridge:${ZRDDS_TAG}-amd64
    container_name: zrdds-kafkabridge
    hostname: kafkabridge
    restart: always
    networks:
      - zaiops

networks:
  zaiops:
    driver: bridge
