#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-LDA (C) Copyright IBM Corp. 2024
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            dockerManage-zrdds.sh
#
# Description:     IBM Z Data Analytics Platform management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Script used to manage the OCI containers of the IBM
#                  Z Data Analytics Platform feature
#
# Syntax:          dockerManage-zrdds.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

NAMESPACE_ZRDDS=zrdds

ZRDDS_DC="-f zrdds-docker-compose.yml --profile ${DISCOVERY_SCENARIO}"
ZRDDS_SETLOGLEVEL=""
ZRDDS_OCI_FEATURE_LABEL="IBM Z AIOps - Z Resource Discovery Data Service"

ZRDDS_CONTAINER_PREFIX=${NAMESPACE_ZRDDS}-

set -a
. ${BASEDIR}/${NEW_CONFIG}
. ${BASEDIR}/${IBM_CONFIG}
. ${BASEDIR}/bin/utils/zrdds-common.sh
set +a


## MAIN
ARG=${1}
case "${ARG}" in
  "help" )
    helpZRDDS
    ;;
  "serviceToContainer" )
    serviceToContainerZRDDS ${2}
    ;;
esac
