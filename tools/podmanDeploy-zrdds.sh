#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-LDA (C) Copyright IBM Corp. 2022, 2023
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            podmanDeploy-zrdds.sh
#
# Description:     IBM Z Resource Discovery Data Service installation
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Installation script used to install or uninstall IBM Z
#                  Data Analytics Platform feature
#
# Syntax:          podmanDeploy-zrdds.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

IMGFILE_ZRDDS=__IMGFILE__

checkPrereqsInstallZRDDS() {
  logToFileAndStdout "${INFOMSG}There are no special installation prerequisites for the Z Resource Discovery Data Service."
}   
    
checkPrereqsRuntimeZRDDS() {
  logToFileAndStdout "${INFOMSG}There are no special runtime prerequisites for the Z Resource Discovery Data Service."
}     
    
checkPrereqsRemoveZRDDS() {
  logToFileAndStdout "${INFOMSG}There are no special uninstallation prerequisites for the Z Resource Discovery Data Service."
}     

migrateConfigZRDDS(){
  DCPATH=${1}
  if [ -f ${DCPATH}/${LEGACY_CONFIG}.old ]
  then
    . ${DCPATH}/${LEGACY_CONFIG}.old
  fi
  if [ -f ${DCPATH}/${OLD_CONFIG} ]
  then
    . ${DCPATH}/${OLD_CONFIG}
  fi  
  # List of ZRDDS variables
  VARLIST_ZRDDS="DISCOVERY_AGENT_COUNT \
    DISCOVERY_LIBRARY_FILE_STORE \
    DISCOVERY_SCENARIO \
    ORIENTDB_HEAP_SIZE \
    ROUTECARD_FILE_STORE"
  for VAR in ${VARLIST_ZRDDS}
  do
    eval VALUE=\"\$${VAR}\"
    if [ "${VALUE}x" == "x" ]
    then # do nothing; value is empty
      :
    elif [[ "${VALUE}" =~ .*\ .* ]]
    then # handle variable values with spaces
      sed -i -e "s%^${VAR}=.*$%${VAR}=\"${VALUE}\"%g" ${DCPATH}/${NEW_CONFIG}.all
    else # handle 'single-word' variable values
      sed -i -e "s%^${VAR}=.*$%${VAR}=${VALUE}%g" ${DCPATH}/${NEW_CONFIG}.all
    fi
  done
}

podmanUnpackZRDDS() {
  DCPATH=${1}
  cd ${SCRIPTDIR}/../zrdds
  echo "Restoring Z Resource Discovery Data Service images from archive..." | tee -a ${LOGFILE}
  echo "" | tee -a ${LOGFILE}
  tar xzf ${IMGFILE_ZRDDS} -C ${DCPATH} 2>&1 | tee -a ${LOGFILE}
  # Append ZRDDS config files to overall config files
  echo "" >> ${DCPATH}/zoa_env.config.all
  cat ${DCPATH}/zoa_env.config >> ${DCPATH}/zoa_env.config.all
  rm -f ${DCPATH}/zoa_env.config
  echo "" >> ${DCPATH}/.zoa_factory.config.all
  cat ${DCPATH}/.zoa_factory.config >> ${DCPATH}/.zoa_factory.config.all
  rm -f ${DCPATH}/.zoa_factory.config
} 

podmanInstallZRDDS() {
  DCPATH=${1}
  cd ${SCRIPTDIR}/../zrdds
  # Remove any old OCI images
  echo "Removing old Z Resource Discovery Data Service images from local repository..." | tee -a ${LOGFILE}
  export RMRESPONSE=Y
  podmanRemoveZRDDS ${DCPATH}
  unset RMRESPONSE
  echo ""
  # Restore OCI images
  cd ${DCPATH}
  echo "Loading new Z Resource Discovery Data Service images into local repository..." | tee -a ${LOGFILE}
  STATUS=SUCCESS
  ${OCI_AGENT} load -q -i zrdds_images.tar >> ${LOGFILE} 2>&1
  RC=$?
  if [ ${RC} -eq 0 ]
  then
    echo "Successfully loaded images." | tee -a ${LOGFILE}
    rm -f zrdds_images.tar
    for IMG in $( ${OCI_AGENT} images | grep zrdds- | awk '{ print $1":"$2 }' )
    do
      BASEIMG=$( echo ${IMG} | awk -F '/' '{ print $NF }' )
      IMGNAME=`echo ${BASEIMG} | cut -f 1 -d ":"`
      IMGTAG=`echo ${BASEIMG} | cut -f 2 -d ":"`
      # Re-tag images for a more user-friendly naming convention
      ${OCI_AGENT} tag icr.io/zoa-oci/${IMGNAME}:${IMGTAG} ibm-zaiops/${IMGNAME}:${IMGTAG} >> ${LOGFILE} 2>&1
      ${OCI_AGENT} rmi icr.io/zoa-oci/${IMGNAME}:${IMGTAG} >> ${LOGFILE} 2>&1
    done
  else
    logToFileAndStdout "${ERRORMSG}Failed to load images. See ${LOGFILE} for details."
    STATUS=FAILURE
  fi
  # Copy this script into install directory
  mkdir -p ${DCPATH}/bin/utils
  cp ${SCRIPTDIR}/../zrdds/utils/podmanDeploy-zrdds.sh ${DCPATH}/bin/utils/
  if [ -f ${DCPATH}/bin/utils/dockerManage-zrdds.sh ] && [ ! -f ${DCPATH}/bin/utils/podmanManage-zrdds.sh ]
  then
    ln -s ${DCPATH}/bin/utils/dockerManage-zrdds.sh ${DCPATH}/bin/utils/podmanManage-zrdds.sh
  fi
  echo "##############################################################" >> ${DCPATH}/bin/sample_remove.rsp
  echo "# Z RESOURCE DISCOVERY DATA SERVICE SETTINGS:" >> ${DCPATH}/bin/sample_remove.rsp
  cat ${SCRIPTDIR}/../zrdds/sample_remove.rsp >> ${DCPATH}/bin/sample_remove.rsp
  # Summarize status
  if [ "${STATUS}" == "SUCCESS" ]
  then
    echo "" | tee -a ${LOGFILE}
    echo "All Z Resource Discovery Data Service images were loaded successfully." | tee -a ${LOGFILE}
  else
    echo "" | tee -a ${LOGFILE}
    echo "Loading of at least one image failed." | tee -a ${LOGFILE}
    echo "The Z Resource Discovery Data Service feature has not been successfully installed." | tee -a ${LOGFILE}
    echo "Please investigate and resolve the issue and re-run this script before attempting to use the feature." | tee -a ${LOGFILE}
    echo "" | tee -a ${LOGFILE}
    exit 1
  fi
}

podmanRemoveZRDDS() {
  DCPATH=${1}
  echo "" | tee -a ${LOGFILE}
  if [ "${DCPATH}x" == "x" ]
  then
    DCPATH="$( cd ${SCRIPTDIR}/.. && pwd )"
  fi
  if [ "${RMRESPONSE}x" == "x" ]
  then
    while [ "${RMRESPONSE}" != "Y" ] && [ "${RMRESPONSE}" != "y" ] &&
          [ "${RMRESPONSE}" != "N" ] && [ "${RMRESPONSE}" != "n" ]
    do
      echo "You are about to remove all Z Resource Discovery Data Service images from the local Podman repository."
      echo "Are you sure you want to continue? (y/N)"
      read -e RMRESPONSE
      RMRESPONSE=${RMRESPONSE:-"n"}   # accept no input as "NO"
    done
  fi
  if [ "${RMRESPONSE}" == "Y" ] || [ "${RMRESPONSE}" == "y" ]
  then
    echo "Shutting down any running containers..."
    for CONT in $( ${OCI_AGENT} ps -a | grep zrdds- | awk '{ print $1 }' )
    do
      echo -n "  Stopping "
      ${OCI_AGENT} stop ${CONT}
      echo -n "  Removing "
      ${OCI_AGENT} rm ${CONT}
    done
    echo "Removing OCI images..." | tee -a ${LOGFILE}
    IMGLIST=$( ${OCI_AGENT} images | grep zrdds- | awk '{ print $3 }' )
    IMGLIST_OBSOLETE=$( ${OCI_AGENT} images | grep -E '(zoa-kafkabridge|postgresql-kafkaconnect)' | awk '{ print $3 }' )
    IMGLIST+=" ${IMGLIST_OBSOLETE}"
    if [ "${IMGLIST}" == "" ]
    then
      echo "No images to remove." | tee -a ${LOGFILE}
    else
      ${OCI_AGENT} rmi -f ${IMGLIST} 2>&1 | tee -a ${LOGFILE}
    fi
    echo "Done." | tee -a ${LOGFILE}
  fi
}

prereqsZRDDS() {
  # Anything to handle for ZRDDS prereqs
  echo "There are no unique prerequisites for the Z Resource Discovery Data Service feature." | tee -a ${LOGFILE}
}

unpackZRDDS() {
  # Unpack ZRDDS package
  podmanUnpackZRDDS ${DCPATH}
}

installZRDDS() {
  # Anything to handle for ZRDDS installation
  podmanInstallZRDDS ${DCPATH}
}   

configZRDDS() {
  # Anything to handle for ZRDDS configuration
  echo "There are no unique configuration actions for the Z Resource Discovery Data Service feature." | tee -a ${LOGFILE}
} 
  
removeZRDDS() {
  # Anything to handle for ZRDDS removal
  podmanRemoveZRDDS ${DCPATH}
} 
  
cleanZRDDS() {
  # Anything to handle for ZRDDS cleanup
  echo "There are no unique cleanup actions for the Z Resource Discovery Data Service feature." | tee -a ${LOGFILE}
} 

getPiExtensionsZRDDS() {
  PI_EXTENSIONS=""
}


## MAIN
ARG=${1}
case "${ARG}" in
  "prereqs" )
    prereqsZRDDS
    ;;
  "unpack" )
    unpackZRDDS
    ;; 
  "install" )
    installZRDDS
    ;;
  "getPiExtensions" )
    getPiExtensionsZRDDS
    ;;
  "config" )
    configZRDDS
    ;;
  "remove" )
    removeZRDDS
    ;; 
  "clean" )
    cleanZRDDS
    ;;
  "checkPrereqsInstall" )
    checkPrereqsInstallZRDDS
    ;;
  "checkPrereqsRuntime" )
    checkPrereqsRuntimeZRDDS
    ;;
  "checkPrereqsRemove" )
    checkPrereqsRuntimeZRDDS
    ;;
  "migrateConfig" )
    migrateConfigZRDDS ${2}
esac

