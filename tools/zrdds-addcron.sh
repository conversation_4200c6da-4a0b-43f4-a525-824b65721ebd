#!/bin/bash
############################################################################
# Licensed Materials - Property of IBM
# 5698-ZAA (C) Copyright IBM Corp. 2021
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
############################################################################

# Brief the purpsoe of the script
echo "This will add a cron job for current user <$(whoami)> to clean the upload
directories that are older than 7 days inside ZRDDS container. The created 
job will run everyday at midnight."


# Ask if user wants to continue or not
echo ""
read -p "Do you want to continue? (yes/no): " reply
case $reply in
  yes) 
    echo "Starting to run the script..."
    ;;
  no) 
    echo "The script is canceled." 
    exit 0
    ;;
  * ) 
    echo "Invalid input!"
    exit 1
    ;;
esac

# Check if cron job exists or not
echo ""
crontab -l |grep 'zrdds-clean.sh'
if [ $? -eq 0 ]; then
  echo "ZRDDS cron job already exists."
  exit 0
fi

# Ask which container engine is used to deploy ZRDDS
echo ""
CONTAINER_ENGINE=""
PS3="Please select the container engine used to deploy ZRDDS: "
ocis=("podman" "docker" "quit")
select oci in "${ocis[@]}"
do
  case $REPLY in
    1)
      CONTAINER_ENGINE=$oci
      break
      ;;
    2)
      CONTAINER_ENGINE=$oci
      break
      ;;
    3)
      echo "No container engine selected!"
      exit 0
      ;;
    *)
      echo "Invalid choice $REPLY."
      ;;
  esac
done


# Detect the container name of ZRDDS
ZRDDS_CONTAINER_NAME=$($CONTAINER_ENGINE ps --filter ancestor=zrdds-api-standalone --format {{.Names}})
#ZRDDS_CONTAINER_NAME=$($CONTAINER_ENGINE ps --filter ancestor=ibm-zaiops --format {{.Names}})
echo ""
if [ -z "$ZRDDS_CONTAINER_NAME" ]
then
  echo "Unable to detect the container name of ZRDDS!"
  echo "Please make sure ZRDDS container is up and running."
  exit 1
else
  count=$(echo "$ZRDDS_CONTAINER_NAME" | wc -l)
  #echo "$count"
  if [ $count -eq 1 ]; then
    echo "ZRDDS container name is detected: $ZRDDS_CONTAINER_NAME"
  else
    echo "Multiple ZRDDS container names are detected:"
    PS3="Please select the container name: "
    select cname in $ZRDDS_CONTAINER_NAME
    do
      if [ $REPLY -ge 1 -a $REPLY -le $count ]
      then
	      echo "Container name selected: $cname"
        ZRDDS_CONTAINER_NAME=$cname
	      break
      else
	      echo "Invalid choice $REPLY."
      fi
    done
  fi
fi

# Generate a clean bash script
cat <<EOF >zrdds-clean.sh
#!/bin/bash
############################################################################
# Licensed Materials - Property of IBM
# 5698-ZAA (C) Copyright IBM Corp. 2021
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
############################################################################

UPLOAD_DIR="/app/zrdds-core/upload-dir/"

echo "\$(date '+%Y-%m-%d %H:%M:%S') - Start to run the clean job"
dcount_before=\$($CONTAINER_ENGINE exec -it $ZRDDS_CONTAINER_NAME ls -lrt \$UPLOAD_DIR | sed 1d | wc -l)
dsize_before=\$($CONTAINER_ENGINE exec -it $ZRDDS_CONTAINER_NAME du -sh  \$UPLOAD_DIR | cut -f1 | awk '{ print $1}')
echo "\$(date '+%Y-%m-%d %H:%M:%S') - Before cleanup, there are \$dcount_before directories(\$dsize_before in total)"

echo "\$(date '+%Y-%m-%d %H:%M:%S') - Remove directories that are older than 7 days"
$CONTAINER_ENGINE exec -it $ZRDDS_CONTAINER_NAME find \$UPLOAD_DIR -maxdepth 1 -type d -mtime +7 |xargs rm -rf
if [ \$? -ne 0 ]; then
  echo "ZRDDS cron job failed to run"
fi

dcount_after=\$($CONTAINER_ENGINE exec -it $ZRDDS_CONTAINER_NAME ls -lrt \$UPLOAD_DIR | sed 1d | wc -l)
dsize_after=\$($CONTAINER_ENGINE exec -it $ZRDDS_CONTAINER_NAME du -sh  \$UPLOAD_DIR | cut -f1 | awk '{ print $1}')
echo "\$(date '+%Y-%m-%d %H:%M:%S') - After cleanup, there are \$dcount_after directories(\$dsize_after in total) remaining"
EOF
chmod +x zrdds-clean.sh

# Create cron job
echo ""
SCRIPT_DIR="$(cd $(dirname $0) && pwd)"
echo "Cleanup script is generated at: $SCRIPT_DIR/zrdds-clean.sh"
echo ""

(crontab -l 2>/dev/null; echo "0 0 * * * ${SCRIPT_DIR}/zrdds-clean.sh > ${SCRIPT_DIR}/zrdds-clean.log  2>&1") | crontab -

rc=$?
if [ $rc -eq 0 ]; then
  echo "ZRDDS cron job has been added successfully.
Job schedule log can be found by using 'sudo grep zrdds-clean /var/log/cron'.
Job clean log for each run can be found ./zrdds-clean.log."
else
  echo "Failed to add ZRDDS cron job."
  exit $rc
fi

