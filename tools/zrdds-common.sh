#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-LDA (C) Copyright IBM Corp. 2024
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            zrdds-common.sh
#
# Description:     Common functions for ZRDDS feature management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Common functions for ZRDDS feature management
#
# Syntax:          Invoked from dockerManage-zrdds.sh / podmanManage-zrdds.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------


ZRDDS_SVC_LIST='@(zrdds-postgresql|zrdds-orientdb|zrdds-core|zrdds-api|zrdds-kafkaconnect|zrdds-kafkabridge)'
ZRDDS_CMD_LIST='@(load-idml|config-ztopology|enable-jcl-processing|load-routecard)'

helpZRDDS() {
  echo -e "Z Resource Discovery Data Service commands:"
  echo -e "  config-ztopology           Configure Z Resource Discovery Data Service"
  echo "ServiceNow CMDB integration commands:"
  echo -e "  enable-jcl-processing      Enable processing of JCL files"
  echo -e "  load-routecard             Load routecard required for JCL processing"
}

serviceToContainerZRDDS() {
  if [[ "$1" == "zrdds-postgresql" ]]; then
    containerName="zrdds-postgresql"
  elif [[ "$1" == "zrdds-orientdb" ]]; then
    containerName="zrdds-orientdb"
  elif [[ "$1" == "zrdds-core" ]]; then
    containerName="zrdds-core"
  elif [[ "$1" == "zrdds-api" ]]; then
    containerName="zrdds-api"
  elif [[ "$1" == "zrdds-kafkaconnect" ]]; then
    containerName="zrdds-kafkaconnect"
  elif [[ "$1" == "zrdds-kafkabridge" ]]; then
    containerName="zrdds-kafkabridge"
  fi
}

config-ztopology() {
  echo ""
  NOW=`date +"%Y%m%d.%H%M%S"`
  TMPPATH=/tmp/zrdds/${NOW}
  mkdir -p ${TMPPATH}
  # Generate ZRDDS config script
  (
  cat <<- CONFIG_END
cd /app
curl -s -H "Content-type: application/json" -H "ApiToken: 56ab24c15b72a457069c5ea42fcfc640" -X POST http://zrdds-core:8080/api/v1/config -d @/app/ztopology-config.json
echo ""
CONFIG_END
  ) > ${TMPPATH}/zrdds_config.sh

  if(isContainerRunning zrdds-core)
  then
    chmod 755 ${TMPPATH}/zrdds_config.sh
    cp ${BASEDIR}/samples/ztopology/ztopology-config.json ${TMPPATH}
    chmod 644 ${TMPPATH}/ztopology-config.json
    # Upload artifacts to ZRDDS container
    tar -C ${TMPPATH} -cf - zrdds_config.sh ztopology-config.json | ${OCI_AGENT} cp - zrdds-core:/app
    # Run ZRDDS configuration
    ${OCI_AGENT} exec zrdds-core bash -c "/app/zrdds_config.sh"
    echo ""
    rm -Rf ${TMPPATH}
  else
    logToStdout "${ERRORMSG}Z Resource Discovery Data Service is not running. Start the service, then try again."
    rm -Rf ${TMPPATH}
    exit 1
  fi
}

load-idml() {
  IDML_OVERRIDE_PATH=${1}
  if [ "${IDML_OVERRIDE_PATH}x" == "x" ]
  then
    if [ "${DISCOVERY_LIBRARY_FILE_STORE}x" == "x" ]
    then
      logToStdout "${ERRORMSG}No Discovery Library File Store path specified. Unable to proceed.\n"
      exit 1
    else
      IDMLPATH=${DISCOVERY_LIBRARY_FILE_STORE}
      logToStdout "${INFOMSG}Using configured Discovery Library File Store (${IDMLPATH})."
    fi
  else
    IDMLPATH=${IDML_OVERRIDE_PATH}
    logToStdout "${INFOMSG}Using override directory (${IDMLPATH})."
  fi
  if [ ! -d ${IDMLPATH} ]
  then
    logToStdout "${ERRORMSG}${IDMLPATH} is not a directory. Unable to proceed.\n"
    exit 1
  fi
  NOW=`date +"%Y%m%d.%H%M%S"`
  TMPPATH=/tmp/zrdds/${NOW}
  mkdir -p ${TMPPATH}
  cd ${IDMLPATH}
  # Package up only files in this immediate directory; ignore any subdirectories
  find -maxdepth 1 -type f -exec tar czf ${TMPPATH}/IdML_${NOW}.tar.gz {} +
  cd -
  # Generate ZRDDS load script
  (
  cat <<- IDMLLOAD_END
cd /app
rm -Rf /app/zrdds-core/upload-dir/*
tar xzf /app/IdML_${NOW}.tar.gz -C /app/zrdds-core/upload-dir && rm -f /app/IdML_${NOW}.tar.gz
FILECOUNT=0
FILEDIR=/app/zrdds-core/upload-dir
cd \${FILEDIR}
for FILE in \$( ls -1 \${FILEDIR}/* )
do
  FILEBASE=\$( basename \${FILE} )
  FILEROOT=\$( echo \${FILEBASE} | rev | cut -f 2- -d "." | rev )
  if [ "\${FILEBASE}" != "\${FILEROOT}.xml" ]
  then
    mv \${FILE} \${FILEDIR}/\${FILEROOT}.xml
  fi
  FILEROOT_NEW=\$( echo \${FILEROOT} | tr -d -c '0-1a-zA-Z.' )
  if [ "\${FILEROOT_NEW}" != "\${FILEROOT}" ]
  then
    mv \${FILEDIR}/\${FILEROOT}.xml \${FILEDIR}/\${FILEROOT_NEW}.xml
  fi
  FILEPATH=\$( curl -s http://zrdds-core:8080/api/v1/file -H 'ApiToken: 56ab24c15b72a457069c5ea42fcfc640' -X POST -H 'Content-Type: multipart/form-data' -H 'accept: */*' -F 'file=@'\${FILEROOT_NEW}'.xml;type=text/xml' 2>&1 | awk '{ print \$1 }' )
  if [ \${FILECOUNT} -eq 0 ]
  then
    FILELIST='"'\${FILEPATH}'"'
  else
    FILELIST=\${FILELIST},'"'\${FILEPATH}'"'
  fi
  ((FILECOUNT++))
done
curl -s -H "Content-Type: application/json" -H 'ApiToken: 56ab24c15b72a457069c5ea42fcfc640' -X POST http://zrdds-core:8080/api/v1/process/PopulateDLA -d '{"fileList":['\${FILELIST}']}'
echo ""
IDMLLOAD_END
  ) > ${TMPPATH}/zrdds_idmlload.sh

  if [ "$( echo ${DISCOVERY_SCENARIO} | tr [:lower:] [:upper:] )" == "ALL" ] || [ "$( echo ${DISCOVERY_SCENARIO} | tr [:lower:] [:upper:] )" == "NO_SNOW" ]
  then
    echo "curl -s -H 'Content-type: application/json' -H 'ApiToken: 56ab24c15b72a457069c5ea42fcfc640' -X GET http://zrdds-core:8080/api/v1/process/CopyIntoOrient/yes" >> ${TMPPATH}/zrdds_idmlload.sh
    echo "" >> ${TMPPATH}/zrdds_idmlload.sh
  fi

  if(isContainerRunning zrdds-core)
  then
    chmod 755 ${TMPPATH}/zrdds_idmlload.sh
    chmod 644 ${TMPPATH}/IdML_${NOW}.tar.gz
    # Upload artifacts to ZRDDS container
    tar -C ${TMPPATH} -cf - zrdds_idmlload.sh IdML_${NOW}.tar.gz | ${OCI_AGENT} cp - ${ZDS_CONTAINER_PREFIX}zrdds-core:/app
    # Run ZRDDS data load
    ${OCI_AGENT} exec ${ZDS_CONTAINER_PREFIX}zrdds-core bash -c "/app/zrdds_idmlload.sh" 2>&1 | tee /tmp/zrdds/IdML_transfer_log.${NOW}
    logToStdout "\n${INFOMSG}Output written to /tmp/zrdds/IdML_transfer_log.${NOW}\n"
    rm -Rf ${TMPPATH}
  else
    logToStdout "${ERRORMSG}Z Resource Discovery Data Service is not running. Start the service, then try again."
    rm -Rf ${TMPPATH}
    exit 1
  fi
}

enable-jcl-processing() {
  echo ""
  NOW=`date +"%Y%m%d.%H%M%S"`
  TMPPATH=/tmp/zrdds/${NOW}
  mkdir -p ${TMPPATH}
  # Generate ZRDDS config script
  (
  cat <<- JCLENABLE_END
cd /app
curl -s -H "Content-type: application/json" -H "ApiToken: 56ab24c15b72a457069c5ea42fcfc640" -X POST http://zrdds-core:8080/api/v1/config -d '
{
  "process":{
    "version":"Process Supported & Format version",
    "process":[
    {
      "process":"JCLStaticScanner",
      "version":"process version",
      "schedule":{},
      "invoke":[
      {
        "version":"plugin version",
        "type":"pipeline",
        "plugin":"jcl",
        "pipeline":"StaticScanner",
        "extract": [],
        "persist":[]
      }]
    },
    {
      "process":"APIDataTransfer",
      "version":"process version",
      "schedule":{},
      "invoke":[
      {
        "version":"plugin version",
        "type":"pipeline",
        "plugin":"jcl",
        "pipeline":"APIDataTransfer",
        "extract":[],
        "persist":[]
      }]
    },
    {
      "process":"FileDataTransfer",
      "version":"process version",
      "schedule":{},
      "invoke":[
      {
        "version":"plugin version",
        "type":"pipeline",
        "plugin":"jcl",
        "pipeline":"FileDataTransfer",
        "extract":[],
        "persist":[]
      }]
    },
    {
      "process":"JCLOperationDataGenerate",
      "version":"process version",
      "schedule":{},
      "invoke":[
      {
        "version":"plugin version",
        "type":"pipeline",
        "plugin":"jcl",
        "pipeline":"JCLOperationDataGenerate",
        "extract":[],
        "persist":[]
      }]
    }]
  },
  "extract":
  {
    "version":"Data Source Supported & Format Version",
    "extract":[]
  },
  "persist":
  {
    "version":"Data Persist Supported & Format Version",
    "persist":[]
  }
}'
echo ""
JCLENABLE_END
  ) > ${TMPPATH}/zrdds_jcl_enable.sh

  if(isContainerRunning zrdds-core)
  then
    chmod 755 ${TMPPATH}/zrdds_jcl_enable.sh
    # Upload artifacts to ZRDDS container
    tar -C ${TMPPATH} -cf - zrdds_jcl_enable.sh | ${OCI_AGENT} cp - zrdds-core:/app
    # Run ZRDDS JCL processing activation
    ${OCI_AGENT} exec zrdds-core bash -c "/app/zrdds_jcl_enable.sh"
    echo ""
    rm -Rf ${TMPPATH}
  else
    logToStdout "${ERRORMSG}Z Resource Discovery Data Service is not running. Start the service, then try again."
    rm -Rf ${TMPPATH}
    exit 1
  fi
}

load-routecard() {
  ROUTECARD_OVERRIDE_PATH=${1}
  echo ""
  if [ "${ROUTECARD_OVERRIDE_PATH}x" == "x" ]
  then
    if [ "${ROUTECARD_FILE_STORE}x" == "x" ]
    then
      logToStdout "${ERRORMSG}No Routcard File Store path specified. Unable to proceed."
      echo ""
      exit 1
    else
      ROUTECARDPATH=${ROUTECARD_FILE_STORE}
      logToStdout "${INFOMSG}Using configured Routecard File Store (${ROUTECARDPATH})."
    fi
  else
    ROUTECARDPATH=${ROUTECARD_OVERRIDE_PATH}
    logToStdout "${INFOMSG}Using override directory (${ROUTECARDPATH})."
  fi
  if [ ! -d ${ROUTECARDPATH} ]
  then
    logToStdout "${ERRORMSG}${ROUTECARDPATH} is not a directory. Unable to proceed.\n"
    exit 1
  fi
  NOW=`date +"%Y%m%d.%H%M%S"`
  TMPPATH=/tmp/zrdds/${NOW}
  mkdir -p ${TMPPATH}
  cd ${ROUTECARDPATH}
  # Package up only files in this immediate directory; ignore any subdirectories
  find -maxdepth 1 -type f -exec tar czf ${TMPPATH}/routecard_${NOW}.tar.gz {} +
  cd -
  # Generate ZRDDS load script
  (
  cat <<- ROUTECARDLOAD_END
cd /app
rm -Rf /app/zrdds-core/upload-dir/*
tar xzf /app/routecard_${NOW}.tar.gz -C /app/zrdds-core/upload-dir && rm -f /app/routecard_${NOW}.tar.gz
for FILE in \$( ls -1 /app/zrdds-core/upload-dir/* )
do
  FILEBASE=\$( basename \${FILE} )
  curl -s -H "Content-type: application/json" -H 'accept: */*' -H "ApiToken: 56ab24c15b72a457069c5ea42fcfc640" -X POST http://zrdds-core:8080/api/v1/process/FileDataTransfer -d '{"fileList":["'\${FILEBASE}'"], "fileType":"ROUTECARD"}'
  echo ""
done
echo ""
ROUTECARDLOAD_END
  ) > ${TMPPATH}/zrdds_routecardload.sh

  if(isContainerRunning zrdds-core)
  then
    chmod 755 ${TMPPATH}/zrdds_routecardload.sh
    chmod 644 ${TMPPATH}/routecard_${NOW}.tar.gz
    # Upload artifacts to ZRDDS container
    tar -C ${TMPPATH} -cf - routecard_${NOW}.tar.gz zrdds_routecardload.sh | ${OCI_AGENT} cp - zrdds-core:/app
    # Run ZRDDS data load
    ${OCI_AGENT} exec zrdds-core bash -c "/app/zrdds_routecardload.sh" 2>&1 | tee /tmp/zrdds/routecard_transfer_log.${NOW}
    echo ""
    logToStdout "${INFOMSG}Output written to /tmp/zrdds/routecard_transfer_log.${NOW}\n"
    rm -Rf ${TMPPATH}
  else
    logToStdout "${ERRORMSG}Z Resource Discovery Data Service is not running. Start the service, then try again."
    rm -Rf ${TMPPATH}
    exit 1
  fi
}
