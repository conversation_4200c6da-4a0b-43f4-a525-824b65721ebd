#!/bin/bash
############################################################################
# Licensed Materials - Property of IBM
# 5698-ZAA (C) Copyright IBM Corp. 2021
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
############################################################################

# Brief the purpsoe of the script
echo "This will extend fields length following the CDM.xsd for v1.3.1.1."

# Ask if user wants to continue or not
echo ""
read -p "Do you want to continue? (yes/no): " reply
case $reply in
  yes) 
    echo "Starting to run the script..."
    ;;
  no) 
    echo "The script is canceled." 
    exit 0
    ;;
  * ) 
    echo "Invalid input!"
    exit 1
    ;;
esac


# Ask which container engine is used to deploy ZRDDS
echo ""
CONTAINER_ENGINE=""
PS3="Please select the container engine used to deploy ZRDDS: "
ocis=("podman" "docker" "quit")
select oci in "${ocis[@]}"
do
  case $REPLY in
    1)
      CONTAINER_ENGINE=$oci
      break
      ;;
    2)
      CONTAINER_ENGINE=$oci
      break
      ;;
    3)
      echo "No container engine selected!"
      exit 0
      ;;
    *)
      echo "Invalid choice $REPLY."
      ;;
  esac
done


# Detect the container name of ZRDDS
ZRDDS_IMAGE_NAME=$(docker ps --format '{{.Image}}' | grep "zrdds-api-standalone")
ZRDDS_CONTAINER_NAME=$($CONTAINER_ENGINE ps --filter ancestor=$ZRDDS_IMAGE_NAME --format {{.Names}})
#ZRDDS_CONTAINER_NAME=$($CONTAINER_ENGINE ps --filter ancestor=ibm-zaiops --format {{.Names}})
echo ""
if [ -z "$ZRDDS_CONTAINER_NAME" ]
then
  echo "Unable to detect the container name of ZRDDS!"
  echo "Please make sure ZRDDS container is up and running."
  exit 1
else
  count=$(echo "$ZRDDS_CONTAINER_NAME" | wc -l)
  #echo "$count"
  if [ $count -eq 1 ]; then
    echo "ZRDDS container name is detected: $ZRDDS_CONTAINER_NAME"
  else
    echo "Multiple ZRDDS container names are detected:"
    PS3="Please select the container name: "
    select cname in $ZRDDS_CONTAINER_NAME
    do
      if [ $REPLY -ge 1 -a $REPLY -le $count ]
      then
	      echo "Container name selected: $cname"
        ZRDDS_CONTAINER_NAME=$cname
	      break
      else
	      echo "Invalid choice $REPLY."
      fi
    done
  fi
fi


echo "start to extend fields legth"
$CONTAINER_ENGINE exec -i $ZRDDS_CONTAINER_NAME bash <<EOF
psql -U postgres << EOF1
\c discovery

ALTER TABLE public.address_space ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.address_space ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.address_space ALTER COLUMN job_name TYPE varchar(192);
ALTER TABLE public.address_space ALTER COLUMN job_type TYPE varchar(255);
ALTER TABLE public.address_space ALTER COLUMN step_name TYPE varchar(192);

ALTER TABLE public.bind_address ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.bind_address ALTER COLUMN path TYPE varchar(192);

ALTER TABLE public.cf_computer_system ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cf_computer_system ALTER COLUMN manufacturer TYPE varchar(192);
ALTER TABLE public.cf_computer_system ALTER COLUMN manufacturer SET NOT NULL;
ALTER TABLE public.cf_computer_system ALTER COLUMN model TYPE varchar(192);
ALTER TABLE public.cf_computer_system ALTER COLUMN serial_number TYPE varchar(192);

ALTER TABLE public.cf_lpar ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cf_lpar ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.cf_lpar ALTER COLUMN vm_id TYPE varchar(192);

ALTER TABLE public.cics_db2_conn ALTER COLUMN dla_id TYPE varchar(255);

ALTER TABLE public.cics_file ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cics_file ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.cics_file ALTER COLUMN file_group TYPE varchar(192);
ALTER TABLE public.cics_file ALTER COLUMN dataset_name TYPE varchar(192);
ALTER TABLE public.cics_file ALTER COLUMN record_size TYPE varchar(192);
ALTER TABLE public.cics_file ALTER COLUMN key_length TYPE varchar(192);
ALTER TABLE public.cics_file ALTER COLUMN open_time TYPE varchar(192);

ALTER TABLE public.cics_plex ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cics_plex ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.cics_plex ALTER COLUMN mvs_sys_id TYPE varchar(192);

ALTER TABLE public.cics_program ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cics_program ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.cics_program ALTER COLUMN prog_name TYPE varchar(192);
ALTER TABLE public.cics_program ALTER COLUMN prog_desc TYPE varchar(255);
ALTER TABLE public.cics_program ALTER COLUMN prog_group TYPE varchar(192);
ALTER TABLE public.cics_program ALTER COLUMN prog_exec_key TYPE varchar(192);
ALTER TABLE public.cics_program ALTER COLUMN prog_data_location TYPE varchar(192);
ALTER TABLE public.cics_program ALTER COLUMN prog_language TYPE varchar(192);

ALTER TABLE public.cics_region ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cics_region ALTER COLUMN region_name TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN appl_id TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN net_id TYPE varchar(255);
ALTER TABLE public.cics_region ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN job_name TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN dfh_cics_type TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN dfh_cics_hlq TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN dfh_le_hlq TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN dfh_region_hlq TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN dfh_region_logstream TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN dfh_region_cicssvc TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN dfh_region_dfltuser TYPE varchar(192);
ALTER TABLE public.cics_region ALTER COLUMN cics_version TYPE varchar(192);

ALTER TABLE public.cics_sit ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cics_sit ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_applid TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_cics_svc TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_cps_conn TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_csd_acc TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_csd_rls TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_dftl_user TYPE varchar(255);
ALTER TABLE public.cics_sit ALTER COLUMN sit_gmtext TYPE varchar(255);
ALTER TABLE public.cics_sit ALTER COLUMN sit_gm_tran TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_irc_strt TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_isc TYPE varchar(192);
ALTER TABLE public.cics_sit ALTER COLUMN sit_jvm_profile_dir TYPE varchar(255);

ALTER TABLE public.cics_sit_overrides ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_applid TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_cics_svc TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_cps_conn TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_csd_acc TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_csd_rls TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_dftl_user TYPE varchar(255);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_gmtext TYPE varchar(255);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_gm_tran TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_irc_strt TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_isc TYPE varchar(192);
ALTER TABLE public.cics_sit_overrides ALTER COLUMN sit_jvm_profile_dir TYPE varchar(255);

ALTER TABLE public.cics_transaction ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.cics_transaction ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.cics_transaction ALTER COLUMN trans_name TYPE varchar(192);
ALTER TABLE public.cics_transaction ALTER COLUMN data_key TYPE varchar(192);
ALTER TABLE public.cics_transaction ALTER COLUMN data_location TYPE varchar(192);
ALTER TABLE public.cics_transaction ALTER COLUMN initial_program TYPE varchar(192);


ALTER TABLE public.db2_buffer_pool ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.db2_buffer_pool ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.db2_buffer_pool ALTER COLUMN name TYPE varchar(192);

ALTER TABLE public.db2_data_sharing_group ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.db2_data_sharing_group ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.db2_data_sharing_group ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.db2_data_sharing_group ALTER COLUMN group_attach_name TYPE varchar(192);
ALTER TABLE public.db2_data_sharing_group ALTER COLUMN group_function TYPE varchar(255);

ALTER TABLE public.db2_database ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.db2_database ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.db2_database ALTER COLUMN name TYPE varchar(192);

ALTER TABLE public.db2_stored_procedure ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.db2_stored_procedure ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.db2_stored_procedure ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.db2_stored_procedure ALTER COLUMN routine_type TYPE varchar(192);
ALTER TABLE public.db2_stored_procedure ALTER COLUMN origin TYPE varchar(192);
ALTER TABLE public.db2_stored_procedure ALTER COLUMN specific_name TYPE varchar(192);
ALTER TABLE public.db2_stored_procedure ALTER COLUMN external_name TYPE varchar(1024);
ALTER TABLE public.db2_stored_procedure ALTER COLUMN coll_id TYPE varchar(192);
ALTER TABLE public.db2_stored_procedure ALTER COLUMN language TYPE varchar(192);

ALTER TABLE public.db2_subsystem ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.db2_subsystem ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.db2_subsystem ALTER COLUMN key_name TYPE varchar(192);
ALTER TABLE public.db2_subsystem ALTER COLUMN subsystem_name TYPE varchar(192);
ALTER TABLE public.db2_subsystem ALTER COLUMN command_prefix_name TYPE varchar(192);
ALTER TABLE public.db2_subsystem ALTER COLUMN controlling_address_space TYPE varchar(192);
ALTER TABLE public.db2_subsystem ALTER COLUMN ddf_location TYPE varchar(192);
ALTER TABLE public.db2_subsystem ALTER COLUMN version_string TYPE varchar(192);

ALTER TABLE public.db2_table ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.db2_table ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.db2_table ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.db2_table ALTER COLUMN table_space TYPE varchar(192);
ALTER TABLE public.db2_table ALTER COLUMN data_base TYPE varchar(192);
ALTER TABLE public.db2_table ALTER COLUMN sharing_group TYPE varchar(192);

ALTER TABLE public.db2_table_space ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.db2_table_space ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.db2_table_space ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.db2_table_space ALTER COLUMN content_type TYPE varchar(192);
ALTER TABLE public.db2_table_space ALTER COLUMN comments TYPE varchar(192);

ALTER TABLE public.fqdn ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.fqdn ALTER COLUMN fqdn TYPE varchar(192);

ALTER TABLE public.ims_database ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.ims_database ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.ims_database ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.ims_database ALTER COLUMN imsdatabasetype TYPE varchar(255);

ALTER TABLE public.ims_program ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.ims_program ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.ims_program ALTER COLUMN label TYPE varchar(192);

ALTER TABLE public.ims_subsystem ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.ims_subsystem ALTER COLUMN Label TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN KeyName TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN SubsystemName TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN VersionString TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN CommandPrefixName TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN ControllingAddressSpace TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN IMSSubsysType TYPE varchar(255);
ALTER TABLE public.ims_subsystem ALTER COLUMN IMSPlexGroupName TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN IRLMGroupName TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN CQSGroupName TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN DatabasesChecksum TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN ProgramsChecksum TYPE varchar(192);
ALTER TABLE public.ims_subsystem ALTER COLUMN TransactionsChecksum TYPE varchar(192);

ALTER TABLE public.ims_sysplex_group ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.ims_sysplex_group ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.ims_sysplex_group ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.ims_sysplex_group ALTER COLUMN group_function TYPE varchar(255);

ALTER TABLE public.ims_transaction ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.ims_transaction ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.ims_transaction ALTER COLUMN name TYPE varchar(192);

ALTER TABLE public.idml_operation_time ALTER COLUMN host_label TYPE varchar(192);

ALTER TABLE public.ip_address ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.ip_address ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.ip_address ALTER COLUMN string_notation TYPE varchar(192);


ALTER TABLE public.ip_interface ALTER COLUMN dla_id TYPE varchar(255);

ALTER TABLE public.lpar ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.lpar ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.lpar ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.lpar ALTER COLUMN lpar_name TYPE varchar(192);
ALTER TABLE public.lpar ALTER COLUMN vm_id TYPE varchar(192);
ALTER TABLE public.lpar ALTER COLUMN model TYPE varchar(192);
ALTER TABLE public.lpar ALTER COLUMN model_id TYPE varchar(192);

ALTER TABLE public.mq_alias_queue ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_alias_queue ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_alias_queue ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_alias_queue ALTER COLUMN target_queue TYPE varchar(192);
ALTER TABLE public.mq_alias_queue ALTER COLUMN default_persistence TYPE varchar(255);
ALTER TABLE public.mq_alias_queue ALTER COLUMN QSDGISP TYPE varchar(192);


ALTER TABLE public.mq_auth_info ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_auth_info ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_auth_info ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_auth_info ALTER COLUMN type TYPE varchar(192);
ALTER TABLE public.mq_auth_info ALTER COLUMN user_name TYPE varchar(192);
ALTER TABLE public.mq_auth_info ALTER COLUMN queue_manager TYPE varchar(192);
ALTER TABLE public.mq_auth_info ALTER COLUMN ldap_server_name TYPE varchar(192);

ALTER TABLE public.mq_buffer_pool ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_buffer_pool ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_buffer_pool ALTER COLUMN id_sequence TYPE varchar(255);

ALTER TABLE public.mq_client_connection_channel ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_client_connection_channel ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_client_connection_channel ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_client_connection_channel ALTER COLUMN QueueSharingGroupDisposition TYPE varchar(192);
ALTER TABLE public.mq_client_connection_channel ALTER COLUMN HeaderCompression TYPE varchar(255);
ALTER TABLE public.mq_client_connection_channel ALTER COLUMN MessageCompression TYPE varchar(255);

ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN QueueSharingGroupDisposition TYPE varchar(192);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN ConnectionName TYPE varchar(192);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN CLWLChannelWeight TYPE varchar(192);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN CLWLChannelPriority TYPE varchar(192);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN CLWLChannelRank TYPE varchar(192);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN PutAuthority TYPE varchar(255);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN HeaderCompression TYPE varchar(255);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN MessageCompression TYPE varchar(255);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN MCAType TYPE varchar(255);
ALTER TABLE public.mq_cluster_receiver_channel ALTER COLUMN NonPersistentMessageSpeed TYPE varchar(255);

ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN QueueSharingGroupDisposition TYPE varchar(192);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN ConnectionName TYPE varchar(192);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN CLWLChannelWeight TYPE varchar(192);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN CLWLChannelPriority TYPE varchar(192);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN CLWLChannelRank TYPE varchar(192);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN HeaderCompression TYPE varchar(255);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN MessageCompression TYPE varchar(255);
ALTER TABLE public.mq_cluster_sender_channel ALTER COLUMN NonPersistentMessageSpeed TYPE varchar(255);

ALTER TABLE public.mq_listener ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_listener ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_listener ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_listener ALTER COLUMN Control TYPE varchar(192);
ALTER TABLE public.mq_listener ALTER COLUMN RemoteName TYPE varchar(192);
ALTER TABLE public.mq_listener ALTER COLUMN QueueManager TYPE varchar(192);

ALTER TABLE public.mq_local_queue ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_local_queue ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_local_queue ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_local_queue ALTER COLUMN DefinitionType TYPE varchar(255);
ALTER TABLE public.mq_local_queue ALTER COLUMN TransmissionUsage TYPE varchar(255);
ALTER TABLE public.mq_local_queue ALTER COLUMN TriggerData TYPE varchar(192);
ALTER TABLE public.mq_local_queue ALTER COLUMN TriggerType TYPE varchar(255);
ALTER TABLE public.mq_local_queue ALTER COLUMN DefaultPersistence TYPE varchar(255);
ALTER TABLE public.mq_local_queue ALTER COLUMN QSGDISP TYPE varchar(192);

ALTER TABLE public.mq_model_queue ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_model_queue ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_model_queue ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_model_queue ALTER COLUMN InitiationQueue TYPE varchar(192);
ALTER TABLE public.mq_model_queue ALTER COLUMN DefinitionType TYPE varchar(255);
ALTER TABLE public.mq_model_queue ALTER COLUMN TransmissionUsage TYPE varchar(255);
ALTER TABLE public.mq_model_queue ALTER COLUMN TriggerData TYPE varchar(192);
ALTER TABLE public.mq_model_queue ALTER COLUMN TriggerType TYPE varchar(255);
ALTER TABLE public.mq_model_queue ALTER COLUMN DefaultPersistence TYPE varchar(255);
ALTER TABLE public.mq_model_queue ALTER COLUMN QSGDISP TYPE varchar(192);

ALTER TABLE public.mq_queue_sharing_group ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_queue_sharing_group ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_queue_sharing_group ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_queue_sharing_group ALTER COLUMN group_function TYPE varchar(255);

ALTER TABLE public.mq_receiver_channel ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_receiver_channel ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_receiver_channel ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_receiver_channel ALTER COLUMN QueueSharingGroupDisposition TYPE varchar(192);
ALTER TABLE public.mq_receiver_channel ALTER COLUMN PutAuthority TYPE varchar(255);
ALTER TABLE public.mq_receiver_channel ALTER COLUMN HeaderCompression TYPE varchar(255);
ALTER TABLE public.mq_receiver_channel ALTER COLUMN MessageCompression TYPE varchar(255);
ALTER TABLE public.mq_receiver_channel ALTER COLUMN NonPersistentMessageSpeed TYPE varchar(255);
ALTER TABLE public.mq_receiver_channel ALTER COLUMN SSLCipherSpecification TYPE varchar(192);


ALTER TABLE public.mq_remote_queue ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_remote_queue ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_remote_queue ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_remote_queue ALTER COLUMN QSGDISP TYPE varchar(192);
ALTER TABLE public.mq_remote_queue ALTER COLUMN RemoteName TYPE varchar(192);
ALTER TABLE public.mq_remote_queue ALTER COLUMN RemoteQueueMgrName TYPE varchar(192);
ALTER TABLE public.mq_remote_queue ALTER COLUMN DefaultPersistence TYPE varchar(255);

ALTER TABLE public.mq_sender_channel ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_sender_channel ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_sender_channel ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_sender_channel ALTER COLUMN QueueSharingGroupDisposition TYPE varchar(192);
ALTER TABLE public.mq_sender_channel ALTER COLUMN ConnectionName TYPE varchar(192);
ALTER TABLE public.mq_sender_channel ALTER COLUMN HeaderCompression TYPE varchar(255);
ALTER TABLE public.mq_sender_channel ALTER COLUMN MessageCompression TYPE varchar(255);
ALTER TABLE public.mq_sender_channel ALTER COLUMN NonPersistentMessageSpeed TYPE varchar(255);
ALTER TABLE public.mq_sender_channel ALTER COLUMN TransmissionQueue TYPE varchar(192);

ALTER TABLE public.mq_server_connection_channel ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_server_connection_channel ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_server_connection_channel ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.mq_server_connection_channel ALTER COLUMN QueueSharingGroupDisposition TYPE varchar(192);
ALTER TABLE public.mq_server_connection_channel ALTER COLUMN HeaderCompression TYPE varchar(255);
ALTER TABLE public.mq_server_connection_channel ALTER COLUMN MessageCompression TYPE varchar(255);

ALTER TABLE public.mq_subsystem ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.mq_subsystem ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.mq_subsystem ALTER COLUMN SubsystemName TYPE varchar(192);
ALTER TABLE public.mq_subsystem ALTER COLUMN CommandPrefixName TYPE varchar(192);
ALTER TABLE public.mq_subsystem ALTER COLUMN ControllingAddressSpace TYPE varchar(192);
ALTER TABLE public.mq_subsystem ALTER COLUMN VersionString TYPE varchar(192);

ALTER TABLE public.module_definition ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.module_definition ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.module_definition ALTER COLUMN type TYPE varchar(192);
ALTER TABLE public.module_definition ALTER COLUMN language TYPE varchar(192);
ALTER TABLE public.module_definition ALTER COLUMN sourcefile TYPE varchar(255);

ALTER TABLE public.prsm_lpar ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.prsm_lpar ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.prsm_lpar ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.prsm_lpar ALTER COLUMN lpar_name TYPE varchar(192);
ALTER TABLE public.prsm_lpar ALTER COLUMN vm_id TYPE varchar(192);
ALTER TABLE public.prsm_lpar ALTER COLUMN model TYPE varchar(192);
ALTER TABLE public.prsm_lpar ALTER COLUMN model_id TYPE varchar(192);

ALTER TABLE public.process_pool ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.process_pool ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.process_pool ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.process_pool ALTER COLUMN cmd_line TYPE varchar(255);

ALTER TABLE public.program_definition ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.program_definition ALTER COLUMN type TYPE varchar(192);
ALTER TABLE public.program_definition ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.program_definition ALTER COLUMN language TYPE varchar(192);
ALTER TABLE public.program_definition ALTER COLUMN loc TYPE varchar(192);
ALTER TABLE public.program_definition ALTER COLUMN locmt TYPE varchar(192);
ALTER TABLE public.program_definition ALTER COLUMN rloc TYPE varchar(192);

ALTER TABLE public.relationship ALTER COLUMN source TYPE varchar(255);
ALTER TABLE public.relationship ALTER COLUMN target TYPE varchar(255);

ALTER TABLE public.relationship_service_now ALTER COLUMN source TYPE varchar(255);
ALTER TABLE public.relationship_service_now ALTER COLUMN target TYPE varchar(255);
ALTER TABLE public.relationship_service_now ALTER COLUMN source_type TYPE varchar(64);
ALTER TABLE public.relationship_service_now ALTER COLUMN target_type TYPE varchar(64);

ALTER TABLE public.sysplex ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.sysplex ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.sysplex ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.sysplex ALTER COLUMN plex_mode TYPE varchar(255);
ALTER TABLE public.sysplex ALTER COLUMN grs_config TYPE varchar(255);

ALTER TABLE public.table_definition ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.table_definition ALTER COLUMN type TYPE varchar(192);
ALTER TABLE public.table_definition ALTER COLUMN name TYPE varchar(192);

ALTER TABLE public.tcp_port ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.tcp_port ALTER COLUMN label TYPE varchar(192);

ALTER TABLE public.trancode_definition ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.trancode_definition ALTER COLUMN type TYPE varchar(192);
ALTER TABLE public.trancode_definition ALTER COLUMN name TYPE varchar(192);


ALTER TABLE public.udp_port ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.udp_port ALTER COLUMN label TYPE varchar(192);

ALTER TABLE public.zos ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.zos ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN smf_id TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN net_id TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN sscp TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN fqdn TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN os_name TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN os_version TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN sys_res_volume TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN ipl_parm_dataset TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN ipl_parm_member TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN ipl_parm_device TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN ipl_parm_volume TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN os_rsu_level TYPE varchar(192);
ALTER TABLE public.zos ALTER COLUMN os_freindly_name TYPE varchar(192);

ALTER TABLE public.z_series_computer ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.z_series_computer ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.z_series_computer ALTER COLUMN name TYPE varchar(192);
ALTER TABLE public.z_series_computer ALTER COLUMN make TYPE varchar(192);
ALTER TABLE public.z_series_computer ALTER COLUMN make DROP NOT NULL;
ALTER TABLE public.z_series_computer ALTER COLUMN manufacturer TYPE varchar(192);
ALTER TABLE public.z_series_computer ALTER COLUMN manufacturer SET NOT NULL;
ALTER TABLE public.z_series_computer ALTER COLUMN model TYPE varchar(192);
ALTER TABLE public.z_series_computer ALTER COLUMN model_id TYPE varchar(192);
ALTER TABLE public.z_series_computer ALTER COLUMN serial_number TYPE varchar(192);
ALTER TABLE public.z_series_computer ALTER COLUMN processing_capacity TYPE varchar(192);
ALTER TABLE public.z_series_computer ALTER COLUMN process_capacity_units TYPE varchar(255);

ALTER TABLE public.z_subsystem ALTER COLUMN dla_id TYPE varchar(255);
ALTER TABLE public.z_subsystem ALTER COLUMN label TYPE varchar(192);
ALTER TABLE public.z_subsystem ALTER COLUMN subsystem_name TYPE varchar(192);
ALTER TABLE public.z_subsystem ALTER COLUMN subsystem_version TYPE varchar(192);
ALTER TABLE public.z_subsystem ALTER COLUMN subsystem_manufacturer TYPE varchar(192);
ALTER TABLE public.z_subsystem ALTER COLUMN type TYPE varchar(192);

EOF1
EOF
echo "finsh extend all fields legth"
