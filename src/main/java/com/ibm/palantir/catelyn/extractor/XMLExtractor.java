/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.extractor;


import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import org.dom4j.Document;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.text.MessageFormat;

public class XMLExtractor extends AbstractExtractor {

    private static final Logger LOG = LoggerFactory.getLogger(APIExtractor.class);

    private URL url;

    private String sourceUrl;

    public XMLExtractor(String extractID) throws ServiceException {
        ConfigManager configManager = ConfigManager.getConfigManager();
        JsonObject jsonObject = configManager.searchConfig("extract", extractID);
        if (!jsonObject.get("type").getAsString().equals("API")) {
            String errCode = ErrorCode.ConfigurationTypeError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), jsonObject.get("type").getAsString(), "api extract");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
        this.sourceUrl = jsonObject.get("url").getAsString();
        try {
            this.url = new URL(this.sourceUrl);
        } catch (Exception e) {
            String errCode = ErrorCode.GeneralError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), e.toString());
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public XMLExtractor() {
    }

    public URL getUrl() {
        return url;
    }

    public void setUrl(URL url) {
        this.url = url;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public Document load() {
        Document document = null;
        try {
            SAXReader saxReader = new SAXReader();
            document = saxReader.read(this.url);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return document;
    }
}
