/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.extractor;


import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.connection.DB2Connection;
import com.ibm.palantir.catelyn.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;

public class DB2Extractor extends AbstractExtractor {

    private static final Logger LOG = LoggerFactory.getLogger(DB2Extractor.class);

    private Statement stmt = null;

//    public DB2Extractor(){
//        DB2Connection connection = new DB2Connection();
//        this.stmt = connection.getJDBCStatement();
//    }

    public DB2Extractor(String extractID) throws ServiceException {
        ConfigManager configManager = ConfigManager.getConfigManager();
        JsonObject jsonObject = configManager.searchConfig("extract", extractID);
        DB2Connection connection = new DB2Connection(jsonObject.get("url").getAsString(), jsonObject.get("user").getAsString(), jsonObject.get("password").getAsString());
        this.stmt = connection.getJDBCStatement();
    }

    public ResultSet getResultSet(String sql) {
        ResultSet resultSet = null;
        try {
            resultSet = this.stmt.executeQuery(sql);
        } catch (Exception e) {
            LOG.error(e.toString());
            e.printStackTrace();
        }
        return resultSet;
    }
}
