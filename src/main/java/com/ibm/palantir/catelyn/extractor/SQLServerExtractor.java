/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.extractor;


import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.connection.SQLServerConnection;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.Statement;
import java.text.MessageFormat;

public class SQLServerExtractor extends AbstractExtractor {

    private static final Logger LOG = LoggerFactory.getLogger(SQLServerExtractor.class);

    private Statement stmt = null;

//    public SQLServerExtractor(){
//        SQLServerConnection connection = new SQLServerConnection();
//        this.stmt = connection.getJDBCStatement();
//    }

    public SQLServerExtractor(String extractID) throws ServiceException {
        ConfigManager configManager = ConfigManager.getConfigManager();
        JsonObject jsonObject = configManager.searchConfig("extract", extractID);
        SQLServerConnection connection = new SQLServerConnection(jsonObject.get("url").getAsString(), jsonObject.get("user").getAsString(), jsonObject.get("password").getAsString());
        this.stmt = connection.getJDBCStatement();
    }

    public ResultSet getResultSet(String sql) throws ServiceException{
        ResultSet resultSet = null;
        try {
            resultSet = this.stmt.executeQuery(sql);
        } catch (Exception e) {
            String errCode = ErrorCode.DBError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), e.toString());
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
        return resultSet;
    }

}
