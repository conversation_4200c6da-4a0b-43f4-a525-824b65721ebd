/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.extractor;


import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.util.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.Map;

public class APIExtractor extends AbstractExtractor {

    private static final Logger LOG = LoggerFactory.getLogger(APIExtractor.class);

    private JsonObject jsonObject;

    public APIExtractor(String extractID) throws ServiceException {
        ConfigManager configManager = ConfigManager.getConfigManager();
        jsonObject = configManager.searchConfig("extract", extractID);
        if (!jsonObject.get("type").getAsString().equals("API")) {
            String errCode = ErrorCode.ConfigurationTypeError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), jsonObject.get("type").getAsString(), "api extract");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public String get(String address, Map<String, String> headers, Map<String, String> parameters, boolean isSSL) throws ServiceException {
        return HttpUtils.get(address, headers, parameters, isSSL);
    }

    public String get(String address, Map<String, String> parameters, boolean isSSL) throws ServiceException {
        return HttpUtils.get(address, null, parameters, isSSL);
    }

    public String get(String address) throws ServiceException {
        return HttpUtils.get(address, null, null, false);
    }

    public String get() throws ServiceException {
        String url = jsonObject.get("url").getAsString();
        if (jsonObject.has("headers")) {

        }
        if (jsonObject.has("parameters")) {

        }
        if (jsonObject.has("isSSL")) {

        }
        return HttpUtils.get(url, null, null, false);
    }

    public String post(String address, Map<String, String> headers, Map<String, String> parameters, Map<String, String> requestBody, boolean isSSL) throws ServiceException {
        return HttpUtils.post(address, headers, parameters, requestBody, isSSL);
    }

    public String post(String address, Map<String, String> parameters, Map<String, String> requestBody, boolean isSSL) throws ServiceException {
        return HttpUtils.post(address, null, parameters, requestBody, isSSL);
    }

    public String post(String address, Map<String, String> requestBody) throws ServiceException {
        return HttpUtils.post(address, null, null, requestBody, false);
    }

    public String post() throws ServiceException {
        String url = jsonObject.get("url").getAsString();
        if (jsonObject.has("headers")) {

        }
        if (jsonObject.has("parameters")) {

        }
        if (jsonObject.has("requestBody")) {

        }
        if (jsonObject.has("isSSL")) {

        }
        return HttpUtils.post(url, null, null, "", false);
    }

    public JsonObject getJsonObject() {
        return jsonObject;
    }

    public void setJsonObject(JsonObject jsonObject) {
        this.jsonObject = jsonObject;
    }
}
