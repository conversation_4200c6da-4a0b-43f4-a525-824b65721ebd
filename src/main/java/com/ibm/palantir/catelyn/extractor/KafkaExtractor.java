package com.ibm.palantir.catelyn.extractor;

import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

public class KafkaExtractor extends AbstractExtractor {

    private static final Logger LOG = LoggerFactory.getLogger(KafkaExtractor.class);

    private KafkaConsumer<String, String> consumer;

    private String topic;

    public KafkaExtractor(String persistID) throws ServiceException {
        ConfigManager configManager = ConfigManager.getConfigManager();
        JsonObject jsonObject = configManager.searchConfig("extract", persistID);
        if (!jsonObject.get("type").getAsString().equals("Kafka")) {
            String errCode = ErrorCode.ConfigurationTypeError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), jsonObject.get("type").getAsString(), "kafka extract");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
        Properties props = new Properties();
        props.put("bootstrap.servers", jsonObject.get("url").getAsString());
        props.put("acks", "all");
        props.put("retries", 0);
        props.put("batch.size", 16384);
        props.put("key.serializer", StringSerializer.class.getName());
        props.put("value.serializer", StringSerializer.class.getName());
        this.consumer = new KafkaConsumer<String, String>(props);
        JsonObject optionConfig = getOptionConfig(jsonObject);
        this.topic = optionConfig.get("topicName").getAsString();
        setProperties(props, optionConfig);
    }

    private JsonObject getOptionConfig(JsonObject config) throws ServiceException {
        if (!config.has("option") || !config.get("option").isJsonObject()) {
            LOG.error("Missing option in persist config");
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Catelyn", "KafkaExtractor", "");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }

        JsonObject optionConfig = config.get("option").getAsJsonObject();

        if (!optionConfig.has("topicName")) {
            LOG.error("Missing topic name in Plugin config");
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Catelyn", "KafkaExtractor", "");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }

        if (optionConfig.has("url")) {
            LOG.error("The url config should be set in extract config");
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Catelyn", "KafkaExtractor", "");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
        return optionConfig;
    }

    private Properties setProperties(Properties properties, JsonObject optionConfig) {
        for (String key : optionConfig.keySet()) {
            if (!key.equals("topicName") && optionConfig.get(key).isJsonPrimitive()) {
                JsonPrimitive value = optionConfig.get(key).getAsJsonPrimitive();
                if (value.isString()) {
                    properties.put(key, value.getAsString());
                } else if (value.isNumber()) {
                    properties.put(key, value.getAsLong());
                } else if (value.isBoolean()) {
                    properties.put(key, value.getAsBoolean());
                }
            }
        }
        return properties;
    }

    public List<String> getData(long timeLong) {
        Duration time = Duration.ofSeconds(timeLong);
        ConsumerRecords<String, String> msgList;
        msgList = consumer.poll(time);
        List<String> list = new ArrayList<>();
        if (null != msgList && msgList.count() > 0) {
            for (ConsumerRecord<String, String> record : msgList) {
                record.value();
                list.add(record.value());
            }
        }
        return list;
    }

    public void close() {
        this.consumer.close();
    }
}
