/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.exception;

public enum ErrorCode {

    AccountError(1000),
    AuthenticationError(1001),

    ConfigurationError(2000),
    ConfigurationInitError(2001),
    ConfigurationStorageTypeError(2002),
    ConfigurationJsonFormatError(2003),
    ConfigurationVerifyError(2004),
    ConfigurationTypeError(2005),
    ConfigurationIDNotFound(2006),


    FileError(3000),
    FileNotExist(3001),
    FileReadError(3002),
    FileCreateError(3003),
    FileIOError(3004),
    FileNoTypeRecord(3005),
    FileStoreEmptyError(3006),
    FileStoreRelativePathError(3007),
    FileStoreError(3008),
    FileDirectoryCreateError(3009),

    DBError(4000),
    DBConnectError(4001),
    DBCloseError(4002),

    CouchbaseError(4100),
    CouchbaseCollectionNotFound(4101),
    CouchbaseNoResultFound(4102),
    CouchbaseIdExist(4103),

    GeneralError(6000),
    HttpRequestError(6001),
    JAXBError(6002),

    ProcessError(7000),
    ProcessCompileError(7001),
    PersistError(7100),
    PersistTypeError(7101),
    PipelineError(7200),
    PipelineNotExistError(7201),

    PipelineTaskError(7202),

    PluginError(8000),
    PluginJAXBError(8001),
    PluginClassNotFoundError(8002),
    PluginIOError(8003),
    PluginIllegalAccessError(8004),
    PluginNullParamError(8005),
    PluginParamFormatError(8006),
    PluginSearchConfigNull(8007),
    PluginInvalidParamError(8008),

    PluginNameError(8101),
    PluginNotLoaded(8102),
    PluginNotExistOrNotFile(8103),
    PluginCacheError(8104),
    PluginInvalidPackage(8105),
    PluginJarCloseError(8106),

    PluginListError(8201),
    PluginReloadError(8202),
    PluginAddError(8203),
    PluginUpsertError(8204),

    ControllerError(9001),
    ApplicationError(9999);

    private int code;

    ErrorCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return this.code;
    }

    public String getCodeStr() {
        return String.valueOf(this.code);
    }
}
