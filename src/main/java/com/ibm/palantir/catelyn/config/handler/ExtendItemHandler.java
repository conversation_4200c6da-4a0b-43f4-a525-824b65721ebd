/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config.handler;

import com.google.gson.*;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.config.entity.ExtendItem;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.util.KeyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;

public class ExtendItemHandler {
    private static final Logger LOG = LoggerFactory.getLogger(ExtendItemHandler.class);

    public void extendListConfig(String jsonStr, String itemType) throws ServiceException {
        try {
            JsonElement jsonElement = JsonParser.parseString(jsonStr);
            extendListConfig(jsonElement, itemType);
        } catch (JsonSyntaxException e) {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), itemType, "The format can not be convert to JsonElement.");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public void extendListConfig(JsonElement jsonElement, String itemType) throws ServiceException {
        if (jsonElement.isJsonObject()) {
            JsonObject jsonObject = jsonElement.getAsJsonObject();
            if (jsonObject.get(itemType).isJsonArray()) {
                JsonArray processes = jsonObject.getAsJsonArray(itemType);
                for (JsonElement processItem : processes) {
                    extractExtendItem(processItem, itemType);
                }
            } else {
                String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), itemType, "The item format is not JsonArray.");
                LOG.error(msg);
                throw new ServiceException(errCode, msg);
            }
        } else {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), itemType, "The type json format is not JsonObject.");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public void extractExtendItem(JsonElement json, String itemType) throws ServiceException {
        Gson gson = new Gson();
        ExtendItem extendItem = gson.fromJson(json, ExtendItem.class);
        if (verifyExtendItem(extendItem) && extendItem.getId() != null) {
            ConfigManager configManager = ConfigManager.getConfigManager();
            configManager.upsertExtend(gson.toJson(extendItem), itemType);
        } else {
            String errCode = ErrorCode.ConfigurationVerifyError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), itemType, json.toString());
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public void extractExtendItem(String jsonStr, String itemType) throws ServiceException {
        extractExtendItem(JsonParser.parseString(jsonStr), itemType);
    }

    public String addExtendItem(JsonElement json, String itemType) throws ServiceException {
        Gson gson = new Gson();
        ExtendItem extendItem = gson.fromJson(json, ExtendItem.class);
        if (verifyExtendItem(extendItem)) {
            String id = KeyUtil.genUniqueKey();
            extendItem.setId(id);
            ConfigManager configManager = ConfigManager.getConfigManager();
            configManager.add(gson.toJson(extendItem), itemType, id);
            return id;
        } else {
            String errCode = ErrorCode.ConfigurationVerifyError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), itemType, json.toString());
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public String addExtendItem(String jsonStr, String itemType) throws ServiceException {
        return addExtendItem(JsonParser.parseString(jsonStr), itemType);
    }

    public boolean checkExtendItemID(String id, String itemType) throws ServiceException {
        ConfigManager configManager = ConfigManager.getConfigManager();
        return configManager.checkID(id, itemType);
    }

    public boolean verifyExtendItem(ExtendItem extendItem) {
        return extendItem.verify();
    }
}
