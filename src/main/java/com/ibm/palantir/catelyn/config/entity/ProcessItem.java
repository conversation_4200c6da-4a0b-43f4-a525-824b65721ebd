/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config.entity;

import com.google.gson.JsonObject;

public class ProcessItem {

    private int id;

    private String type;

    private String process;

    private String version;

    private int timestamp;

    private JsonObject option;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getProcess() {
        return process;
    }

    public void setProcess(String process) {
        this.process = process;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(int timestamp) {
        this.timestamp = timestamp;
    }

    public JsonObject getOption() {
        return option;
    }

    public void setOption(JsonObject option) {
        this.option = option;
    }

    public boolean verify() {
        if (this.type != null && this.type.equals("process") && this.process != null && this.version != null) {
            return true;
        } else {
            return false;
        }
    }
}
