/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config.entity;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

public class PipelineItem {

    private int id;

    private String type;

    private String version;

    private int timestamp;

    private String plugin;

    private String pipeline;

    private JsonArray extract;

    private JsonArray persist;

    private JsonObject option;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(int timestamp) {
        this.timestamp = timestamp;
    }

    public String getPlugin() {
        return plugin;
    }

    public void setPlugin(String plugin) {
        this.plugin = plugin;
    }

    public String getPipeline() {
        return pipeline;
    }

    public void setPipeline(String pipeline) {
        this.pipeline = pipeline;
    }

    public JsonArray getExtract() {
        return extract;
    }

    public void setExtract(JsonArray extract) {
        this.extract = extract;
    }

    public JsonArray getPersist() {
        return persist;
    }

    public void setPersist(JsonArray persist) {
        this.persist = persist;
    }

    public JsonObject getOption() {
        return option;
    }

    public void setOption(JsonObject option) {
        this.option = option;
    }

    public boolean verify() {
        if (this.type != null && this.type.equals("pipeline") && this.version != null && this.pipeline != null && this.plugin != null) {
            return true;
        } else {
            return false;
        }
    }
}
