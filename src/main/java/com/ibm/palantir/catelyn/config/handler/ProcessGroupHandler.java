/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config.handler;

import com.google.gson.*;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.config.entity.PipelineItem;
import com.ibm.palantir.catelyn.config.entity.ProcessGroup;
import com.ibm.palantir.catelyn.config.entity.ProcessItem;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;


public class ProcessGroupHandler {

    private static final Logger LOG = LoggerFactory.getLogger(ProcessGroupHandler.class);

    public void processGroupConfig(String jsonStr) throws ServiceException {
        try {
            JsonElement jsonElement = JsonParser.parseString(jsonStr);
            processGroupConfig(jsonElement);
        } catch (JsonSyntaxException e) {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public void processGroupConfig(JsonElement json) throws ServiceException {
        if (json.isJsonObject()) {
            JsonObject jsonObject = json.getAsJsonObject();
            if (jsonObject.get("process").isJsonArray()) {
                JsonArray processes = jsonObject.getAsJsonArray("process");
                for (JsonElement processItem : processes) {
                    extractProcessGroup(processItem);
                }
            } else {
                String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
                LOG.error(msg);
                throw new ServiceException(errCode, msg);
            }
        } else {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public String extractProcessGroup(JsonElement json) throws ServiceException {
        Gson gson = new Gson();
        if (!json.isJsonObject()) {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
        ProcessGroup processGroup = gson.fromJson(json, ProcessGroup.class);
        if (processGroup.verify()) {
            processGroup = verifyInvoke(processGroup);
            ConfigManager configManager = ConfigManager.getConfigManager();
            configManager.upsertProcess(gson.toJson(processGroup));
            return "Process handler upload successfully!";
        } else {
            String errCode = ErrorCode.ConfigurationVerifyError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "process",
                    String.join(",", json.getAsJsonObject().keySet()));
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }


    public String extractProcessGroup(String jsonStr) throws ServiceException {
        try {
            return extractProcessGroup(JsonParser.parseString(jsonStr));
        } catch (JsonSyntaxException e) {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }


    public ProcessGroup verifyInvoke(ProcessGroup processGroup) throws ServiceException {
        Gson gson = new Gson();
        JsonArray invoke = processGroup.getInvoke();
        for (int i = 0; i < invoke.size(); i++) {
            JsonElement invokeElement = invoke.get(i);
            if (invokeElement.isJsonObject()) {
                JsonObject invokeObject = invokeElement.getAsJsonObject();
                String invokeType = invokeObject.get("type").getAsString();
                if (invokeType.equals("process")) {
                    ProcessItem processItem = gson.fromJson(invokeObject, ProcessItem.class);
                    if (!processItem.verify()) {
                        String errCode = ErrorCode.ConfigurationVerifyError.getCodeStr();
                        String msg = MessageFormat.format(MsgTemp.get(errCode), "process",
                                String.join(",", invokeObject.keySet()));
                        LOG.error(msg);
                        throw new ServiceException(errCode, msg);
                    }
                } else if (invokeType.equals("pipeline")) {
                    PipelineItem pipelineItem = gson.fromJson(invokeObject, PipelineItem.class);
                    pipelineItem = verifyPipelineItem(pipelineItem);
                    if (!pipelineItem.verify()) {
                        String errCode = ErrorCode.ConfigurationVerifyError.getCodeStr();
                        String msg = MessageFormat.format(MsgTemp.get(errCode), "pipeline",
                                String.join(",", invokeObject.keySet()));
                        LOG.error(msg);
                        throw new ServiceException(errCode, msg);
                    }
                    invoke.set(i, gson.toJsonTree(pipelineItem));
                } else {
                    String errCode = ErrorCode.ConfigurationTypeError.getCodeStr();
                    String msg = MessageFormat.format(MsgTemp.get(errCode), invokeType, "invoke");
                    LOG.error(msg);
                    throw new ServiceException(errCode, msg);
                }
            } else {
                String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "invoke");
                LOG.error(msg);
                throw new ServiceException(errCode, msg);
            }
        }
        processGroup.setInvoke(invoke);
        return processGroup;
    }

    private PipelineItem verifyPipelineItem(PipelineItem pipelineItem) throws ServiceException {
        Gson gson = new Gson();
        JsonArray extract = pipelineItem.getExtract();
        ExtendItemHandler extendItemHandler = new ExtendItemHandler();
        for (int i = 0; i < extract.size(); i++) {
            JsonElement extractItem = extract.get(i);
            if (extractItem.isJsonObject()) {
                String id = extendItemHandler.addExtendItem(extractItem, "extract");
                extract.set(i, gson.toJsonTree(id));
            } else {
                if (!extendItemHandler.checkExtendItemID(extractItem.getAsString(), "extract")) {
                    String errCode = ErrorCode.ConfigurationIDNotFound.getCodeStr();
                    String msg = MessageFormat.format(MsgTemp.get(errCode), "extract", extractItem.getAsString());
                    LOG.error(msg);
                    throw new ServiceException(errCode, msg);
                }
            }
        }
        pipelineItem.setExtract(extract);
        JsonArray persist = pipelineItem.getPersist();
        for (int i = 0; i < persist.size(); i++) {
            JsonElement persistItem = persist.get(i);
            if (persistItem.isJsonObject()) {
                String id = extendItemHandler.addExtendItem(persistItem, "persist");
                persist.set(i, gson.toJsonTree(id));
            } else {
                if (!extendItemHandler.checkExtendItemID(persistItem.getAsString(), "persist")) {
                    String errCode = ErrorCode.ConfigurationIDNotFound.getCodeStr();
                    String msg = MessageFormat.format(MsgTemp.get(errCode), "persist", persistItem.getAsString());
                    LOG.error(msg);
                    throw new ServiceException(errCode, msg);
                }
            }
        }
        pipelineItem.setPersist(persist);
        return pipelineItem;
    }


    public String extractConfig(String jsonStr) throws ServiceException {
        ExtendItemHandler extendItemHandler = new ExtendItemHandler();
        try {
            JsonElement jsonElement = JsonParser.parseString(jsonStr);
            if (jsonElement.isJsonObject()) {
                JsonObject jsonObject = jsonElement.getAsJsonObject();
                if (!jsonObject.has("extract") || !jsonObject.has("persist") || !jsonObject.has("process")) {
                    String errCode = ErrorCode.ConfigurationVerifyError.getCodeStr();
                    String msg = MessageFormat.format(MsgTemp.get(errCode), "config", String.join(",", jsonObject.keySet()));
                    LOG.error(msg);
                    throw new ServiceException(errCode, msg);
                }
                JsonElement extract = jsonObject.get("extract");
                extendItemHandler.extendListConfig(extract, "extract");
                JsonElement persist = jsonObject.get("persist");
                extendItemHandler.extendListConfig(persist, "persist");
                JsonElement process = jsonObject.get("process");
                processGroupConfig(process);
            } else {
                String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "config");
                LOG.error(msg);
                throw new ServiceException(errCode, msg);
            }
            return "Config upload successfully!";
        } catch (JsonSyntaxException e) {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "config");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }
}
