/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config.entity;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

public class ProcessGroup {

    private int id;

    private String process;

    private String version;

    private int timestamp;

    private JsonObject schedule;

    private JsonArray invoke;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getProcess() {
        return process;
    }

    public void setProcess(String process) {
        this.process = process;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(int timestamp) {
        this.timestamp = timestamp;
    }

    public JsonObject getSchedule() {
        return schedule;
    }

    public void setSchedule(JsonObject schedule) {
        this.schedule = schedule;
    }

    public JsonArray getInvoke() {
        return invoke;
    }

    public void setInvoke(JsonArray invoke) {
        this.invoke = invoke;
    }

    public boolean verify() {
        if (this.process != null && this.version != null) {
            return true;
        } else {
            return false;
        }
    }
}
