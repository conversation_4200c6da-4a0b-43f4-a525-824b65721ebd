/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config.manager;

import com.couchbase.client.core.env.IoConfig;
import com.couchbase.client.java.Bucket;
import com.couchbase.client.java.Cluster;
import com.couchbase.client.java.Collection;
import com.couchbase.client.java.env.ClusterEnvironment;
import com.couchbase.client.java.query.QueryResult;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.util.AesUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

import static com.couchbase.client.java.ClusterOptions.clusterOptions;


public class CouchBaseManager implements Manager {

    private static final Logger LOG = LoggerFactory.getLogger(CouchBaseManager.class);
    private Cluster cluster;

    public CouchBaseManager(String url, String user, String password) {

        ClusterEnvironment env = ClusterEnvironment.builder().ioConfig(IoConfig.enableDnsSrv(false)).build();
        this.cluster = Cluster.connect(
                url,
                clusterOptions(user, password).environment(env)
        );
    }

    public JsonObject searchProcess(String name) throws ServiceException {
        try {
            Collection collection = openBucket("process").defaultCollection();

            if (collection.exists(name).exists()) {
                String resultStr = collection.get(name).contentAsObject().toString();
                JsonObject result = JsonParser.parseString(resultStr).getAsJsonObject();
                return result;
            } else {
                String errCode = ErrorCode.CouchbaseCollectionNotFound.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
                LOG.error(msg);
                throw new ServiceException(errCode, msg);
            }
        } catch (JsonSyntaxException e) {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        } catch (Exception e) {
            String errCode = ErrorCode.CouchbaseError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), e.toString());
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }


    public JsonObject searchConfig(String type, String id) throws ServiceException {
        try {
            Collection collection = openBucket(type).defaultCollection();
            if (collection.exists(id).exists()) {
                JsonObject result = JsonParser.parseString(collection.get(id).contentAsObject().toString()).getAsJsonObject();
                return AesUtil.decryptJson(result);
            } else {
                String errCode = ErrorCode.CouchbaseCollectionNotFound.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
                LOG.error(msg);
                throw new ServiceException(errCode, msg);
            }
        } catch (JsonSyntaxException e) {
            String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        } catch (Exception e) {
            String errCode = ErrorCode.CouchbaseError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), e.toString());
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }


    public List searchType(String type) throws ServiceException {
        try {
            QueryResult queryResult = this.cluster.query("select * from " + type);
            List<com.couchbase.client.java.json.JsonObject> list = queryResult.rowsAsObject();
            if (list.isEmpty()) {
                String errCode = ErrorCode.CouchbaseNoResultFound.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), type);
                LOG.error(msg);
                throw new ServiceException(errCode, msg);
            }
            List result = new ArrayList();
            for (com.couchbase.client.java.json.JsonObject jsonObject : list) {
                result.add(JsonParser.parseString(jsonObject.getObject(type).toString()));
            }
            return result;
        } catch (Exception e) {
            String errCode = ErrorCode.CouchbaseCollectionNotFound.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "process");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }


    public void upsertExtend(String json, String name) throws ServiceException {
        com.couchbase.client.java.json.JsonObject jsonObject = com.couchbase.client.java.json.JsonObject.fromJson(json);
        String id = jsonObject.getString("id");
        store(jsonObject, name, id, true);
    }

    public void upsertProcess(String json) throws ServiceException {
        com.couchbase.client.java.json.JsonObject jsonObject = com.couchbase.client.java.json.JsonObject.fromJson(json);
        String name = jsonObject.getString("process");
        store(jsonObject, "process", name, true);
    }

    public void add(String json, String name, String id) throws ServiceException {
        com.couchbase.client.java.json.JsonObject jsonObject = com.couchbase.client.java.json.JsonObject.fromJson(json);
        store(jsonObject, name, id, true);
    }


    private void store(com.couchbase.client.java.json.JsonObject jsonObject, String name, String id, Boolean replace) throws ServiceException {
        Bucket bucket = openBucket(name);
        Collection collection = bucket.defaultCollection();
        jsonObject = encryptJson(jsonObject);
        // Store the Document
        try {
            if (!collection.exists(id).exists() || replace) {
                collection.upsert(id, jsonObject);
            } else {
                String errCode = ErrorCode.CouchbaseIdExist.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), id, "name");
                LOG.error(msg);
                throw new ServiceException(errCode, msg);
            }
        } catch (RuntimeException e) {
            String errCode = ErrorCode.CouchbaseError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), e.toString());
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public boolean checkID(String id, String bucketName) {
        try {
            Bucket bucket = openBucket(bucketName);
            Collection collection = bucket.defaultCollection();
            return collection.exists(id).exists();
        } catch (Exception e) {
            LOG.error(e.toString());
            return false;
        }
    }

    public Bucket openBucket(String name) {
        return this.cluster.bucket(name);
    }

    public static com.couchbase.client.java.json.JsonObject encryptJson(com.couchbase.client.java.json.JsonObject jsonObject){
        if(jsonObject.containsKey("user")){
            String user = jsonObject.get("user").toString();
            jsonObject.put("user", AesUtil.Encrypt(user));
        }
        if(jsonObject.containsKey("password")){
            String password = jsonObject.get("password").toString();
            jsonObject.put("password", AesUtil.Encrypt(password));
        }
        if(jsonObject.containsKey("option")){
            com.couchbase.client.java.json.JsonObject option = jsonObject.getObject("option");
            if(option.containsKey("user")){
                String user = option.get("user").toString();
                option.put("user", AesUtil.Encrypt(user));
            }
            if(option.containsKey("password")){
                String password = option.get("password").toString();
                option.put("password", AesUtil.Encrypt(password));
            }
            jsonObject.put("option", option);
        }
        return jsonObject;
    }
}
