/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config.manager;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.util.AesUtil;
import com.ibm.palantir.catelyn.util.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;


public class FileManager implements Manager {

    private static final Logger LOG = LoggerFactory.getLogger(FileManager.class);
    private JsonObject configObject;

    private String filePath;

    public FileManager(String url) throws ServiceException {
        this.filePath = url;
        String jsonStr = FileUtils.readStringFromFile(this.filePath);
        this.configObject = JsonParser.parseString(jsonStr).getAsJsonObject();
    }

    public JsonObject searchProcess(String name) throws ServiceException {
        JsonObject process = configObject.getAsJsonObject("process");
        if (process.has(name)) {
            return process.getAsJsonObject(name).deepCopy();
        } else {
            String errCode = ErrorCode.ProcessError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "There is no process named " + name);
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }


    public JsonObject searchConfig(String type, String id) {
        JsonObject typeItem = configObject.getAsJsonObject(type).getAsJsonObject(id).deepCopy();
        return AesUtil.decryptJson(typeItem);
    }

    public List searchType(String type) throws ServiceException {
        List result = new ArrayList();
        JsonObject typeObject = configObject.getAsJsonObject(type);
        for (String key : typeObject.keySet()) {
            result.add(typeObject.getAsJsonObject(key));
        }
        if (result.size() == 0) {
            String errCode = ErrorCode.FileNoTypeRecord.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), type, this.filePath);
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
        return result;
    }


    public void upsertExtend(String json, String name) throws ServiceException {
        JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
        String id = jsonObject.get("id").getAsString();
        store(jsonObject, name, id, true);
    }

    public void upsertProcess(String json) throws ServiceException {
        JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
        String name = jsonObject.get("process").getAsString();
        store(jsonObject, "process", name, true);
    }

    public void add(String json, String name, String id) throws ServiceException {
        JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
        store(jsonObject, name, id, true);
    }


    private void store(JsonObject jsonObject, String name, String id, Boolean replace) throws ServiceException {
        jsonObject = AesUtil.encryptJson(jsonObject);
        if (configObject.has(name)) {
            JsonObject typeConfig = configObject.getAsJsonObject(name);
            if (typeConfig.has(id) && !replace) {
                return;
            } else {
                typeConfig.add(id, jsonObject);
                configObject.add(name, typeConfig);
            }
        } else {
            JsonObject typeConfig = new JsonObject();
            typeConfig.add(id, jsonObject);
            configObject.add(name, typeConfig);
        }
        FileUtils.saveFile(filePath, configObject.toString());
    }

    public boolean checkID(String id, String bucketName) {
        if (configObject.has(bucketName)) {
            JsonObject typeConfig = configObject.getAsJsonObject(bucketName);
            return typeConfig.has(id);
        } else {
            return false;
        }
    }

//    public String getFileContent(String fileName) throws ServiceException {
//        StringBuffer content = new StringBuffer();
//        BufferedReader reader = null;
//        try {
//            File file = new File(fileName);
//            if (file.exists() && file.isFile()) {
//                reader = new BufferedReader(new FileReader(file));
//                String line = null;
//                while ((line = reader.readLine()) != null) {
//                    content.append(line);
//                }
//            } else {
//                content.append("{}");
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            String errCode = ErrorCode.FileReadError.getCodeStr();
//            String msg = MessageFormat.format(MsgTemp.get(errCode), fileName);
//            LOG.error(msg);
//            throw new ServiceException(errCode, msg);
//        } finally {
//            try {
//                if (reader != null)
//                    reader.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//                String errCode = ErrorCode.FileIOError.getCodeStr();
//                String msg = MessageFormat.format(MsgTemp.get(errCode), fileName, e.getMessage());
//                LOG.error(msg);
//                throw new ServiceException(errCode, msg);
//            }
//        }
//
//        return content.toString();
//    }
//
//
//    private void saveFile(String fileName, String data) throws ServiceException {
//        BufferedWriter writer = null;
//        File file = new File(fileName);
//        if (!file.exists()) {
//            try {
//                file.createNewFile();
//            } catch (IOException e) {
//                e.printStackTrace();
//                String errCode = ErrorCode.FileCreateError.getCodeStr();
//                String msg = MessageFormat.format(MsgTemp.get(errCode), fileName);
//                LOG.error(msg);
//                throw new ServiceException(errCode, msg);
//            }
//        }
//        try {
//            writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file, false), "UTF-8"));
//            writer.write(data);
//        } catch (IOException e) {
//            e.printStackTrace();
//            String errCode = ErrorCode.FileIOError.getCodeStr();
//            String msg = MessageFormat.format(MsgTemp.get(errCode), fileName, e.getMessage());
//            LOG.error(msg);
//            throw new ServiceException(errCode, msg);
//        } finally {
//            try {
//                if (writer != null) {
//                    writer.close();
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//                String errCode = ErrorCode.FileIOError.getCodeStr();
//                String msg = MessageFormat.format(MsgTemp.get(errCode), fileName, e.getMessage());
//                LOG.error(msg);
//                throw new ServiceException(errCode, msg);
//            }
//        }
//
//    }
}
