/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config.manager;

import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.exception.ServiceException;

import java.util.List;

public interface Manager {

    JsonObject searchProcess(String name) throws ServiceException;

    JsonObject searchConfig(String type, String id) throws ServiceException;

    List searchType(String type) throws ServiceException;

    void upsertExtend(String json, String name) throws ServiceException;

    void upsertProcess(String json) throws ServiceException;

    void add(String json, String name, String id) throws ServiceException;

    boolean checkID(String id, String bucketName) throws ServiceException;
}
