/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config;

import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.manager.CouchBaseManager;
import com.ibm.palantir.catelyn.config.manager.FileManager;
import com.ibm.palantir.catelyn.config.manager.Manager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.List;


public class ConfigManager {

    private static final Logger LOG = LoggerFactory.getLogger(ConfigManager.class);
    private volatile static ConfigManager configManager;
    private Manager manager;

    private ConfigManager(String type, String url, String user, String password) throws ServiceException {
        if (type.equals("couchbase")) {
            this.manager = new CouchBaseManager(url, user, password);
        } else if (type.equals("file")) {
            this.manager = new FileManager(url);
        } else {
            String errCode = ErrorCode.ConfigurationStorageTypeError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), type);
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
    }

    public static ConfigManager getConfigManager(String type, String url, String user, String password) throws ServiceException {
        if (configManager == null) {
            synchronized (ConfigManager.class) {
                if (configManager == null) {
                    configManager = new ConfigManager(type, url, user, password);
                }
            }
        }
        return configManager;
    }

    public static ConfigManager getConfigManager() throws ServiceException {
        if (configManager == null) {
            synchronized (ConfigManager.class) {
                if (configManager == null) {
                    String errCode = ErrorCode.ConfigurationInitError.getCodeStr();
                    String msg = MsgTemp.get(errCode);
                    LOG.error(msg);
                    throw new ServiceException(errCode, msg);
                }
            }
        }
        return configManager;
    }

    public JsonObject searchProcess(String name) throws ServiceException {
        return this.manager.searchProcess(name);
    }


    public JsonObject searchConfig(String type, String id) throws ServiceException {
        return this.manager.searchConfig(type, id);
    }

    public List searchType(String type) throws ServiceException {
        return this.manager.searchType(type);
    }


    public void upsertExtend(String json, String name) throws ServiceException {
        this.manager.upsertExtend(json, name);
    }

    public void upsertProcess(String json) throws ServiceException {
        this.manager.upsertProcess(json);
    }

    public void add(String json, String name, String id) throws ServiceException {
        this.manager.add(json, name, id);
    }

    public boolean checkID(String id, String bucketName) throws ServiceException {
        return this.manager.checkID(id, bucketName);
    }
}
