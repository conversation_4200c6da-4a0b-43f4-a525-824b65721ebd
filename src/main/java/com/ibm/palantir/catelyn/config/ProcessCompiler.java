/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.config;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.catelyn.util.GsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class ProcessCompiler {

    private static final Logger LOG = LoggerFactory.getLogger(ProcessCompiler.class);

    public List<PipelineConf> getProcesses(String process, int recursionNum) throws Exception {
        try {
            ConfigManager configManager = ConfigManager.getConfigManager();
            JsonObject jsonObject = configManager.searchProcess(process);
            Type type = new TypeToken<List<String>>() {
            }.getType();
            if (jsonObject == null) {
                LOG.error("The process named {} is not exist!", process);
                return new ArrayList();
            }
            JsonArray array = jsonObject.get("invoke").getAsJsonArray();
            List processList = new ArrayList<PipelineConf>();
            for (int i = 0; i < array.size(); i++) {
                JsonObject object = array.get(i).getAsJsonObject();
                if (object.get("type").getAsString().equals("pipeline")) {
                    PipelineConf pipelineConf = new PipelineConf();
                    pipelineConf.setPlugin(object.get("plugin").getAsString());
                    pipelineConf.setPipeline(object.get("pipeline").getAsString());
                    object.remove("plugin");
                    object.remove("pipeline");
                    pipelineConf.setConfig(object.toString());
                    pipelineConf.setExtract(GsonUtils.fromJson(object.get("extract").toString(), type));
                    pipelineConf.setPersist(GsonUtils.fromJson(object.get("persist").toString(), type));
                    processList.add(pipelineConf);
                } else if (object.get("type").getAsString().equals("process")) {
                    if (recursionNum - 1 == 0) {
                        LOG.error("This recursion has been called more than limited times, stop it!");
                    } else {
                        processList.addAll(getProcesses(object.get("process").getAsString(), recursionNum - 1));
                    }
                } else {
                    LOG.error("This process object type: {} is not supported!", object.get("type").getAsString());
                }
            }
            return processList;
        } catch (Exception e) {
            LOG.error(e.toString());
            throw e;
        }
    }

    public List<PipelineConf> getProcesses(String process) throws Exception {
        return getProcesses(process, 5);
    }
}
