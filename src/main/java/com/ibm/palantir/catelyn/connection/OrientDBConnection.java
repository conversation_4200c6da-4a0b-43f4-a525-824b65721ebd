/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.connection;

import org.apache.tinkerpop.gremlin.orientdb.OrientGraph;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrientDBConnection extends AbstractConnection {

    private static final Logger LOG = LoggerFactory.getLogger(OrientDBConnection.class);

    private String orientUrl = null;
    private String orientUser = null;
    private String orientPassword = null;

//    public OrientDBConnection(){
//        this.orientUrl = "remote:localhost/Test";
//        this.orientUser = "admin";
//        this.orientPassword = "";
//    }

    public OrientDBConnection(String orientUrl) {
        this.orientUrl = orientUrl;
    }

    public OrientDBConnection(String orientUrl, String orientUser, String orientPassword) {
        this.orientUrl = orientUrl;
        this.orientUser = orientUser;
        this.orientPassword = orientPassword;
    }


    public GraphTraversalSource getTraversal() {
        GraphTraversalSource g = OrientGraph.open(this.orientUrl, this.orientUser, this.orientPassword).traversal();
        return g;
    }

    public String getOrientUrl() {
        return orientUrl;
    }

    public void setOrientUrl(String orientUrl) {
        this.orientUrl = orientUrl;
    }

    public String getOrientUser() {
        return orientUser;
    }

    public void setOrientUser(String orientUser) {
        this.orientUser = orientUser;
    }

    public String getOrientPassword() {
        return orientPassword;
    }

    public void setOrientPassword(String orientPassword) {
        this.orientPassword = orientPassword;
    }
}
