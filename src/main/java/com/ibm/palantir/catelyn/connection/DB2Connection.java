/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.connection;

import java.sql.Connection;
import java.sql.*;

public class DB2Connection extends AbstractConnection {

    private String url = "*************************************";
    //private String url = "***************************************";
    private String user = "ysong";
    private String password = "";

    //private String url = "******************************************";
    //private String user = "ysong";
    //private String password = "";

    private Connection connection = null;

    public DB2Connection() {
    }

    public DB2Connection(String url, String user, String password) {
        this.url = url;
        this.user = user;
        this.password = password;
    }

    public static void main(String[] args) throws SQLException {

        DB2Connection db2tool = new DB2Connection();
        Connection myconn = db2tool.getConn();
        //pgtool.queryUpdate(myconn, "insert into test values (1,'smoon','man')");
        //ResultSet rs = pgtool.query(myconn, "select * from dla_data");
        String query = "select NAME, TSNAME, DBNAME from SYSIBM.SYSTABLES where NAME=?";
        String tableName = "CUSTOMER";

        PreparedStatement pstmt = (PreparedStatement) myconn.prepareStatement(query);
        pstmt.setString(1, tableName);
        ResultSet rs = pstmt.executeQuery();


        //ResultSet rs = db2tool.query(myconn, "select NAME, TSNAME, DBNAME from SYSIBM.SYSTABLES where NAME=");

        while (rs.next()) {
            String name = rs.getString("NAME");
            //String dla_id = rs.getString("dla_id");
            String tsname = rs.getString("TSNAME");
            String dbname = rs.getString("DBNAME");
            System.out.println("name:" + name + " tsname：" + tsname + " dbname: " + dbname);

        }
        myconn.close();

    }

    public String getDB2Subsystem() {
        String[] strArr = this.url.split("/");
        return strArr[strArr.length - 1];
    }

    public Connection getConn() {
        try {
            Class.forName("com.ibm.db2.jcc.DB2Driver").newInstance();
            String urlUP = url + ":user=" + user + ";password=" + password + ";";
            //connection = DriverManager.getConnection(url, user, password);
            connection = DriverManager.getConnection(urlUP);
        } catch (InstantiationException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (SQLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return connection;
    }

    public Statement getJDBCStatement() {

        Statement stmt = null;
        try {
            Class.forName("com.ibm.db2.jcc.DB2Driver");
            Connection conn = DriverManager.getConnection(this.url, this.user, this.password);
            stmt = conn.createStatement();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return stmt;
    }

    public ResultSet query(Connection conn, String sql) {
        PreparedStatement pStatement = null;
        ResultSet rs = null;
        try {
            pStatement = conn.prepareStatement(sql);
            rs = pStatement.executeQuery();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return rs;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
