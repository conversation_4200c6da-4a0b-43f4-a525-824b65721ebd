/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.connection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

// just backup for accessing PostgresQL, if the spring-data-jpa is invalid
public class PostgresQLConnection extends AbstractConnection {

    private static final Logger LOG = LoggerFactory.getLogger(PostgresQLConnection.class);

    private String url = "***************************************************************";
    private String user = "postgres";
    private String password = "";

//    public PostgresQLConnection() {
//    }

    private Connection conn = null;
    private Statement stmt = null;

    public PostgresQLConnection(String url, String user, String password) {
        this.url = url;
        this.user = user;
        this.password = password;
    }

    public Statement getJDBCStmt() {
        try {
            Class.forName("org.postgresql.Driver");
            this.conn = DriverManager.getConnection(this.url, this.user, this.password);
            this.stmt = conn.createStatement();
        } catch (Exception e) {
            LOG.error(e.toString());
            e.printStackTrace();
        }
        return this.stmt;
    }

    public void close() {
        try {
            if (this.conn != null) {
                this.conn.close();
            }
            if (this.stmt != null) {
                this.stmt.close();
            }
        } catch (Exception e) {
            LOG.error(e.toString());
            e.printStackTrace();
        }
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUser() {
        return this.user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
