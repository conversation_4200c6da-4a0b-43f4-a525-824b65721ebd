/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.entity.dla.postgresql;

import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@IdClass(RelationPK.class)
@Table(name = "relationship_service_now")
@Data
public class RelationshipServiceNow {

    @Id
    @Column(name = "name", length = 32, nullable = false)
    private String name;
    @Id
    @Column(name = "source_id", nullable = false)
    private String sourceId;
    @Id
    @Column(name = "target_id", nullable = false)
    private String targetId;
    @Column(name = "source", nullable = false)
    private String source;
    @Column(name = "target", nullable = false)
    private String target;
    @Column(name = "source_prefix", length = 128, nullable = false)
    private String sourcePrefix;
    @Column(name = "target_prefix", length = 128, nullable = false)
    private String targetPrefix;
    @Column(name = "source_type", length = 64)
    private String sourceType;
    @Column(name = "target_type", length = 64)
    private String targetType;
    // sometimes the data model want to reverse the relationship and set another
    // name.
    @Column(name = "reverse_name", length = 32)
    private String reverseName;
    @Column(name = "data_source", length = 8)
    private String dataSource = "DLA";
    @Column(name = "soft_del", columnDefinition = "boolean default false")
    private Boolean softDel = false;
    @Column(name = "scan_date", length = 32)
    private String scanDate;
    @Column(name = "kafka_send_date")
    private Instant kafkaSendDate;

    public RelationshipServiceNow() {
        super();
    }

    public RelationshipServiceNow(String name, String sourceId, String targetId, String source, String target,
            String sourcePrefix, String targetPrefix, String sourceType, String targetType,
            String reverseName, String dataSource, String scanDate, Instant kafkaSendDate){
        super();
        this.name = name;
        this.sourceId = sourceId;
        this.targetId = targetId;
        this.source = source;
        this.target = target;
        this.sourcePrefix = sourcePrefix;
        this.targetPrefix = targetPrefix;
        this.sourceType = sourceType;
        this.targetType = targetType;
        this.reverseName = reverseName;
        this.dataSource = dataSource;
        this.scanDate = scanDate;
        this.kafkaSendDate = kafkaSendDate;
    }

    public RelationshipServiceNow(String name, String sourceId, String targetId, String source, String target,
            String prefix, String sourceType, String targetType, String dataSource, String scanDate, Instant kafkaSendDate) {
        super();
        this.name = name;
        this.sourceId = sourceId;
        this.targetId = targetId;
        this.source = source;
        this.target = target;
        this.sourcePrefix = prefix;
        this.targetPrefix = prefix;
        this.sourceType = sourceType;
        this.targetType = targetType;
        this.dataSource = dataSource;
        this.scanDate = scanDate;
        this.kafkaSendDate = kafkaSendDate;
    }
}
