/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.entity.ad;
// Generated Apr 7, 2020, 6:10:58 AM by Hibernate Tools 5.4.13.Final


import jakarta.persistence.*;
import java.util.Date;

/**
 * Programs generated by hbm2java
 */
@Entity
@Table(name = "Programs"
        , schema = "dbo"
        , catalog = "EZ_GenAPP"
)
public class Programs implements java.io.Serializable {


    private int programId;
    private String programName;
    private Integer hasNestedPrograms;
    private String ancestor;
    private Integer paragraphsCounter;
    private Date etimeStamp;
    private Integer occurId;
    private Integer programTypeId;

    public Programs() {
    }


    public Programs(int programId, String programName) {
        this.programId = programId;
        this.programName = programName;
    }

    public Programs(int programId, String programName, Integer hasNestedPrograms, String ancestor, Integer paragraphsCounter, Date etimeStamp, Integer occurId, Integer programTypeId) {
        this.programId = programId;
        this.programName = programName;
        this.hasNestedPrograms = hasNestedPrograms;
        this.ancestor = ancestor;
        this.paragraphsCounter = paragraphsCounter;
        this.etimeStamp = etimeStamp;
        this.occurId = occurId;
        this.programTypeId = programTypeId;
    }

    @Id


    @Column(name = "ProgramID", unique = true, nullable = false)
    public int getProgramId() {
        return this.programId;
    }

    public void setProgramId(int programId) {
        this.programId = programId;
    }


    @Column(name = "ProgramName", nullable = false, length = 250)
    public String getProgramName() {
        return this.programName;
    }

    public void setProgramName(String programName) {
        this.programName = programName;
    }


    @Column(name = "HasNestedPrograms")
    public Integer getHasNestedPrograms() {
        return this.hasNestedPrograms;
    }

    public void setHasNestedPrograms(Integer hasNestedPrograms) {
        this.hasNestedPrograms = hasNestedPrograms;
    }


    @Column(name = "Ancestor", length = 250)
    public String getAncestor() {
        return this.ancestor;
    }

    public void setAncestor(String ancestor) {
        this.ancestor = ancestor;
    }


    @Column(name = "ParagraphsCounter")
    public Integer getParagraphsCounter() {
        return this.paragraphsCounter;
    }

    public void setParagraphsCounter(Integer paragraphsCounter) {
        this.paragraphsCounter = paragraphsCounter;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "eTimeStamp", length = 23)
    public Date getEtimeStamp() {
        return this.etimeStamp;
    }

    public void setEtimeStamp(Date etimeStamp) {
        this.etimeStamp = etimeStamp;
    }


    @Column(name = "OccurID")
    public Integer getOccurId() {
        return this.occurId;
    }

    public void setOccurId(Integer occurId) {
        this.occurId = occurId;
    }


    @Column(name = "ProgramTypeID")
    public Integer getProgramTypeId() {
        return this.programTypeId;
    }

    public void setProgramTypeId(Integer programTypeId) {
        this.programTypeId = programTypeId;
    }

}


