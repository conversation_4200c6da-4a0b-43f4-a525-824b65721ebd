/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2024
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.repository.manage;

import com.ibm.palantir.catelyn.jpa.entity.manage.DLADataTrace;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DLADataTraceRepository extends JpaRepository<DLADataTrace, Long> {

//    @Query("SELECT t FROM DLADataTrace t WHERE t.id=:id")
    DLADataTrace findById(@Param("id") String id);

    @Query(value = "SELECT * FROM dla_data_trace t WHERE t.file_path=:filePath ORDER BY id ASC LIMIT :limit OFFSET :offset", nativeQuery = true)
    List<DLADataTrace> findByFilePathLimitOffsetOrderById(@Param("filePath") String filePath, @Param("limit") Integer limit, @Param("offset") Integer offset);
}
