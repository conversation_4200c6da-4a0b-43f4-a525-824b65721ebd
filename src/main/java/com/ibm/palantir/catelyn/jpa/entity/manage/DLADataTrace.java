/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2024
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.entity.manage;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "dla_data_trace")
@Data  
public class DLADataTrace {
    @Id
    @Column(name = "id")
    private String id;
    @Column(name = "dla_id")
    private String dlaId;
    @Column(name = "file_path")
    private String filePath;
    @Column(name = "scan_date")
    private String scanDate;
}
