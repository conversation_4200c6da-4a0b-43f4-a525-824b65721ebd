/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 202021-202521
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.entity.dla.postgresql;

import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "z_series_computer",
        indexes = {@Index(columnList = "dla_id"), @Index(columnList = "prefix_id,dla_id")}
)
@Data
public class ZSeriesComputer {

    @Id
    @Column(name = "id")
    private String id;
    @Column(name = "dla_id", nullable = false)
    private String dlaId;
    @Column(name = "prefix_id", length = 128, nullable = false)
    private String prefixId;
    @Column(name = "label", length = 192)
    private String label;
    @Column(name = "name", length = 192)
    private String name;
    @Column(name = "make", length = 192)
    private String make;
    @Column(name = "manufacturer", length = 192, nullable = false)
    private String manufacturer;
    @Column(name = "model", length = 192, nullable = false)
    private String model;
    @Column(name = "model_id", length = 192)
    private String modelId;
    @Column(name = "serial_number", length = 192, nullable = false)
    private String serialNumber;
    @Column(name = "processing_capacity", length = 192)
    private String processingCapacity;
    @Column(name = "process_capacity_units")
    private String processCapacityUnits;
    @Column(name = "memory_size")
    private Long memorySize;
    @Column(name = "num_cpus")
    private Integer numCPUs;
    @Column(name = "cpu_speed")
    private Long cpuSpeed;
    @Column(name = "data_source", length = 8)
    private String dataSource = "DLA";
    @Column(name = "scan_date", length = 32)
    private String scanDate;
    @Column(name = "soft_del", columnDefinition = "boolean default false")
    private Boolean softDel = false;
    @Column(name = "kafka_send_date")
    private Instant kafkaSendDate;
}
