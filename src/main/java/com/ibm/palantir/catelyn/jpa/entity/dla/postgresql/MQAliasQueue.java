/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.entity.dla.postgresql;

import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "mq_alias_queue",
        indexes = {@Index(columnList = "dla_id"), @Index(columnList = "prefix_id,dla_id")}
)
@Data
public class MQAliasQueue {

    @Id
    @Column(name = "id")
    private String id;
    @Column(name = "dla_id", nullable = false)
    private String dlaId;
    @Column(name = "prefix_id", length = 128, nullable = false)
    private String prefixId;
    @Column(name = "data_source", length = 8)
    private String dataSource = "DLA";
    @Column(name = "scan_date", length = 32)
    private String scanDate;
    @Column(name = "soft_del", columnDefinition = "boolean default false")
    private Boolean softDel = false;
    @Column(name = "label", length = 192)
    private String label;
    @Column(name = "name", length = 192)
    private String name;
    @Column(name = "description")
    private String description;
    @Column(name = "get")
    private Boolean get;
    @Column(name = "put")
    private Boolean put;
    @Column(name = "target_queue", length = 192)
    private String targetQueue;
    @Column(name = "default_persistence")
    private String defaultPersistence;
    @Column(name = "QSDGISP", length = 192)
    private String qsgdisp;
    @Column(name = "kafka_send_date")
    private Instant kafkaSendDate;
}
