/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.repository.dla;

import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.RelationshipServiceNow;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RelationshipServiceNowRepository extends JpaRepository<RelationshipServiceNow, Long> {

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.name=:name and t.source=:source and t.target=:target")
    List<RelationshipServiceNow> getItemsByDetail4Dla(@Param("name") String name, @Param("source") String source, @Param("target") String target);

    @Query("SELECT t FROM Relationship t WHERE t.name=:name and t.source=:source and t.sourcePrefix = :sourcePrefix and t.targetPrefix=:targetPrefix and t.target=:target")
    RelationshipServiceNow getItemByDetail4Prefix(@Param("name") String name, @Param("source") String source, @Param("sourcePrefix") String sourcePrefix,
                                        @Param("target") String target, @Param("targetPrefix") String targetPrefix);

    @Query("SELECT t FROM Relationship t WHERE t.name=:name and t.sourceId=:sourceId and t.targetId=:targetId")
    RelationshipServiceNow getItemByDetail4Id(@Param("name") String name, @Param("sourceId") String sourceId, @Param("targetId") String targetId);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.name=:name")
    List<RelationshipServiceNow> getRelationshipServiceNowListByName(@Param("name") String name);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.source=:source")
    List<RelationshipServiceNow> getRelationshipServiceNowListBySource(@Param("source") String source);

    @Query(value = "SELECT * FROM relationship_service_now ORDER BY name ASC LIMIT :limit OFFSET :offset", nativeQuery = true)
    List<RelationshipServiceNow> getRelationshipServiceNowListByNameLimitOffset(@Param("limit") Integer limit, @Param("offset") Integer offset);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.source like :source")
    List<RelationshipServiceNow> getRelationshipServiceNowListBySourceLike(@Param("source") String source);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.target like :target")
    List<RelationshipServiceNow> getRelationshipServiceNowListByTargetLike(@Param("target") String target);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.source like :source and t.target like :target")
    List<RelationshipServiceNow> getRelationshipServiceNowListBySourceAndTargetLike(@Param("source") String source, @Param("target") String target);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.sourceType=:sourceType")
    List<RelationshipServiceNow> getRelationshipServiceNowListBySourceType(@Param("sourceType") String sourceType);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.sourceType=:targetType")
    List<RelationshipServiceNow> getRelationshipServiceNowListByTargetType(@Param("targetType") String targetType);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.sourceType=:sourceType and t.targetType=:targetType")
    List<RelationshipServiceNow> getRelationshipServiceNowListByBothType(@Param("sourceType") String sourceType, @Param("targetType") String targetType);

    @Query("SELECT t FROM RelationshipServiceNow t WHERE t.softDel=false AND (t.sourceId=:id OR t.targetId=:id)")
    List<RelationshipServiceNow> findActiveBySourceIdOrTargetId(@Param("id") String id);

    @Query(value = "SELECT * FROM relationship_service_now t WHERE t.soft_del=false AND t.source_id in :idList LIMIT :limit OFFSET :offset", nativeQuery = true)
    List<RelationshipServiceNow> findActiveBySourceIdsLimitOffset(@Param("idList") List<String> idList, @Param("limit") Integer limit, @Param("offset") Integer offset);

    @Query(value = "SELECT * FROM relationship_service_now t WHERE t.soft_del=false AND t.target_id in :idList LIMIT :limit OFFSET :offset", nativeQuery = true)
    List<RelationshipServiceNow> findActiveByTargetIdsLimitOffset(@Param("idList") List<String> idList, @Param("limit") Integer limit, @Param("offset") Integer offset);

    @Override
    List<RelationshipServiceNow> findAll();
}
