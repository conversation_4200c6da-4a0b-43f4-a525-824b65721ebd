/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.repository.dla;

import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ProgramDefinition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProgramDefinitionRepository extends JpaRepository<ProgramDefinition, Long> {
    @Query("SELECT t FROM ProgramDefinition t WHERE t.dlaId=:dlaId")
    ProgramDefinition getProgramDefinitionByDLAId(@Param("dlaId") String dlaId);

    @Query("SELECT t FROM ProgramDefinition t WHERE t.dlaId=:dlaId AND t.prefixId=:prefixId")
    ProgramDefinition findByDlaIdAndPrefixId(@Param("dlaId") String dlaId, @Param("prefixId") String prefixId);

    @Query("SELECT t FROM ProgramDefinition t WHERE t.id=:id")
    ProgramDefinition findById(@Param("id") String id);

    @Query("SELECT t FROM ProgramDefinition t")
    List<ProgramDefinition> getProgramDefinitionList();
}
