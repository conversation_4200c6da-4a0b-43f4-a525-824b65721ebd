/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.catelyn.jpa.entity.dla.postgresql;

import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "zos",
        indexes = {@Index(columnList = "dla_id"), @Index(columnList = "prefix_id,dla_id")}
)
@Data
public class ZOS {

    @Id
    @Column(name = "id")
    private String id;
    @Column(name = "dla_id", nullable = false)
    private String dlaId;
    @Column(name = "prefix_id", length = 128, nullable = false)
    private String prefixId;
    @Column(name = "label", length = 192)
    private String label;
    @Column(name = "name", length = 192)
    private String name;
    @Column(name = "smf_id", length = 192)
    private String smfId;
    @Column(name = "net_id", length = 192)
    private String netID;
    @Column(name = "sscp", length = 192)
    private String sscp;
    @Column(name = "fqdn", length = 192)
    private String fqdn;
    @Column(name = "os_name", length = 192)
    private String osName;
    @Column(name = "os_version", length = 192)
    private String osVersion;
    @Column(name = "sys_res_volume", length = 192)
    private String sysResVolume;
    @Column(name = "ipl_parm_dataset", length = 192)
    private String iplParmDataset;
    @Column(name = "ipl_parm_member", length = 192)
    private String iplParmMember;
    @Column(name = "ipl_parm_device", length = 192)
    private String iplParmDevice;
    @Column(name = "ipl_parm_volume", length = 192)
    private String iplParmVolume;
    @Column(name = "os_rsu_level", length = 192)
    private String osRSULevel;
    @Column(name = "os_freindly_name", length = 192)
    private String osFriendlyName;
    @Column(name = "data_source", length = 8)
    private String dataSource = "DLA";
    @Column(name = "scan_date", length = 32)
    private String scanDate;
    @Column(name = "soft_del", columnDefinition = "boolean default false")
    private Boolean softDel = false;
    @Column(name = "kafka_send_date")
    private Instant kafkaSendDate;
}
