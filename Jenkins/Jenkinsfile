#!/usr/bin/env groovy
/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

import groovy.json.JsonOutput

echo "Branch: ${env.BRANCH_NAME}"

env.BASE_PATH = "com/ibm/nazare/discovery"
env.COMPONENT = "catelyn"
env.VERSION = "1.4.0"
def uploadSubFolder = "$BASE_PATH/$COMPONENT/$VERSION"

def jfUrl = "https://na.artifactory.swg-devops.com/artifactory"
def jfCredentialsId = 'jfrog-token'
def jfServer = Artifactory.newServer url: "${jfUrl}", credentialsId: "${jfCredentialsId}"

def now = new Date()
def buildTime = now.format("yyyyMMdd.HHmmss", TimeZone.getTimeZone('UTC'))

pipeline {
   agent {
      node {
         label 'zds-node'
      }
   }

   // Code checkout is implicit
   stages {
      stage('Preparation') {
         steps {
            sh """
            echo "Maven version"
            mvn --version
            """
         }
      }

      stage('Build') {
         steps {
            sh """
            echo "Building"
            mvn compile
            """
         }
      }

      stage('Test') {
         environment {
            scannerHome = tool 'SonarQubeScanner'
         }
         steps {
            withSonarQubeEnv('ZDSSonarQube') {
               echo "Testing & Analyzing"
               sh "mvn org.jacoco:jacoco-maven-plugin:prepare-agent -Dmaven.test.failure.ignore=false -Dproject.version=${env.VERSION}-${buildTime} package sonar:sonar"
            }
         }
      }

      stage('Archive') {
         steps {
            script {
               if (env.BRANCH_NAME ==~ /(master|dev|PI\d+-Fix\d+|v\d+(\.\d+)*-FP\d+)/) {
                  archiveArtifacts "target/*${buildTime}.jar"

                  def uploadSpec = """{
                  "files": [
                        {
                           "pattern": "target/*${buildTime}.jar",
                           "target": "sys-nazare-cicd-ext-maven-local/${uploadSubFolder}/"
                        }
                     ]
                  }"""
                  jfServer.upload spec: uploadSpec
               } else {
                  echo "Archive stage is skipped for feature branches!"
               }
            }
         }
      }

      // stage ('Deploy') {
      //    steps {
      //       script {
      //          if (env.BRANCH_NAME ==~ /(master|dev|PI\d+-Fix\d+)/) {
      //             sh "mkdir -p automation"

      //             dir("automation") {
      //                git credentialsId: 'bf455b81-d487-4f9a-9d12-aab1482511d6', url: 'https://github.ibm.com/palantir/automation.git'
      //                dir("deploy") {
      //                   withCredentials([string(credentialsId: '32689d01-67b8-4306-a71a-7e416449d52a', variable: 'TOKEN')]) {
      //                      sh "ansible-playbook ${env.COMPONENT}.yml -i hosts --extra-vars \"artifactory_api_key=${TOKEN}\""
      //                   }
      //                }
      //             }
      //          } else {
      //             echo "Deploy stage is skipped for feature branches!"
      //          }
      //       }
      //    }
      // }
   }

   post {
      success {
         echo "Build ${buildTime} passed"
         notifyBuild(buildTime, "SUCCESS")
         deleteDir()
      }
      failure {
         echo "Build ${buildTime} failed"
         notifyBuild(buildTime, "FAILED")
         deleteDir()
      }
   }
}

// Notify slack for branch builds
def notifyBuild(String buildTime, String buildStatus = 'STARTED')
{
    // build status of null means successful
    buildStatus = buildStatus ?: 'SUCCESS'

    // Default values
    def colorName = 'RED'
    def colorCode = '#FF0000'
    def gitCommitMessage = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()

    def subject = "${env.JOB_NAME} - Build #${env.BUILD_NUMBER} : ${buildStatus}"
    def innerText = "Build started at: ${buildTime}"

    if (buildStatus == 'STARTED')
    {
        colorName = 'YELLOW'
        colorCode = '#FFFF00'
    }
    else if (buildStatus == 'SUCCESS')
    {
        colorName = 'GREEN'
        colorCode = '#00FF00'
    }
    else
    {
        colorName = 'RED'
        colorCode = '#FF0000'
    }

   def attachments = [
      [
         color: colorCode,
         pretext: 'IBM Z Discovery CI/CD',
         title: subject,
         title_link: env.BUILD_URL,
         text: innerText,
         fields: [[
               title: 'Container',
               value: env.COMPONENT,
               short: true
            ],
            [
               title: 'Branch',
               value: env.BRANCH_NAME,
               short: true
            ],
            [
               title: 'Version',
               value: env.VERSION,
               short: true
            ],
            [
               title: 'Node',
               value: env.NODE_NAME,
               short: true
            ]],
         footer: 'IBM Z Discovery',
         footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png'
      ]
   ]

   slackSend(attachments: attachments)
}