// Using Scripted Pipeline for maximum flexibility  

// This is a custom data structure we'll use to define our parallel builds:
List<StageDef> stageDefs = [
        new StageDef("s390x"),
        new StageDef("amd64")]

// The 'branches' structure is a map from branch name to branch code. This is the
// argument we'll give to the 'parallel' build step later:
def branches = [:]

def jfUrl = "https://na.artifactory.swg-devops.com/artifactory"
def jfCredentialsId = 'zoa-af-credentials-zds'
def jfServer = Artifactory.newServer url: "${jfUrl}", credentialsId: "${jfCredentialsId}"

def lastSuccessfulBuild(buildsNotPassed, build) {
    if ((build != null) && (build.number > 1) && (build.result != 'SUCCESS')) {
        buildsNotPassed.add(build)
        try {
            lastSuccessfulBuild(buildsNotPassed, build.getPreviousBuild())
        } catch (Exception ex) {
            echo "No prior build available."
        }
    }
}

@NonCPS
def getChangeLog(buildsNotPassed) {
    def log = ""
    for (int x = 0; x < buildsNotPassed.size(); x++) {
        def currentBuild = buildsNotPassed[x];
        def changeLogSets = currentBuild.changeSets
        for (int i = 0; i < changeLogSets.size(); i++) {
            def entries = changeLogSets[i].items
            for (int j = 0; j < entries.length; j++) {
                def entry = entries[j]
                def date = new Date(entry.timestamp)
                def sdf = date.format("yyyy-MM-dd hh:mm:ss").toString()
                // Exclude commits by functional ID since they don't reflect real code changes
                // Nobody checking in real code changes has any business using this ID
                String entryAuthor = entry.author
                String entryMsg = entry.msg
                if (entryAuthor.equals('c-nx1u897') && entryMsg.startsWith('Build ID')) {
                    echo "Discarding non-functional commit ${entry.commitId}."
                } else {
                    log += "* ${entry.commitId} ${entry.msg} (${entry.author} @ ${sdf}) \n"
                }
            }
        }
    }
    return log;
}

buildsNotPassed = []
lastSuccessfulBuild(buildsNotPassed, currentBuild);
echo "Number of reviewed builds: " + buildsNotPassed.size()
echo "List of reviewed builds:"
for (int i = 0; i < buildsNotPassed.size(); i++) {
    echo "    #" + buildsNotPassed[i].number
}

properties ([
  [ $class: 'BuildDiscarderProperty', strategy:
    [ $class: 'LogRotator',
        artifactDaysToKeepStr: '10',
        artifactNumToKeepStr: '10',
        daysToKeepStr: '10',
        numToKeepStr: '10'
    ]
  ]
])

// Loop through the stage definitions and define the parallel stages:
for (stageDef in stageDefs) {

    // Never inline this!
    String interp = stageDef.interp
    String buildArch = interp
    if (interp == 'amd64' ) {
      buildArch = 'x86_64'
    }

    String branchName = "Build ZOA ZRDDS support for ${interp}"
    String labelName = "docker-${interp}"
    String gitBranch = "${env.BRANCH_NAME}"
    String fallbackBranch = "develop"
    String buildId = "${env.BUILD_NUMBER}"
    String gitOrg = "palantir"
    String gitRepo = "zrdds-zoa-support"

    def nowDate = new Date()
    final buildDate = nowDate.format('yyyyMMdd-HHmm')
    print("Label name is ${labelName}.")
    print("Git branch is ${gitBranch}.")
    print("Jenkins build ID is ${buildId}.")
    print("Build date is ${buildDate}.")

    branches[branchName] = {
        // Start the branch with a node definition. We explicitly exclude the
        // master node, so only the two slaves will do builds:
        node(labelName) {
            def workspace = env.WORKSPACE
            print("Current workspace is ${workspace}.")
            String targetDir = 'GITWORK/' + gitRepo
            sh(script: 'rm -Rf GITWORK && mkdir -p ' + targetDir, returnStdout: true)
            checkout([
                    $class: 'GitSCM',
                    branches: [[name: 'refs/heads/' + gitBranch]],
                    extensions: [
                            [$class: 'RelativeTargetDirectory', relativeTargetDir: targetDir],
                            [$class: 'LocalBranch', localBranch: "**"]
                    ],
                    userRemoteConfigs: [[url: 'https://github.ibm.com/' + gitOrg + '/' + gitRepo + '.git', credentialsId: '52b125d0-fe3a-433b-8778-cf6528ca1a37']]
            ])
            def changeLog = getChangeLog(buildsNotPassed)
            echo "Change log since last successful build:"
            echo "---------------------------------------"
            echo "\'${changeLog}\'"
            // First build cannot determine a 'last successful' build and must always run to establish a baseline
            if ((changeLog == '') && (currentBuild.number > 1)) {
                // echo "No changes found; will not run build."
                echo "No changes found."
                // currentBuild.result = 'NOT_BUILT'
            }
            // } else {
                sh(script: 'rm -Rf GITWORK && mkdir -p ' + targetDir, returnStdout: true)
                sh(script: 'rm -Rf DIST && mkdir -p DIST', returnStdout: true)
                withCredentials([usernamePassword(credentialsId: '52b125d0-fe3a-433b-8778-cf6528ca1a37',
                      passwordVariable: 'PASSKEY',
                      usernameVariable: 'BUILDER')]) {
                  sh(script: 'cd GITWORK && git clone https://discovery.applicationid:${PASSKEY}@github.ibm.com/' + gitOrg + '/' + gitRepo + '.git -b ' + gitBranch, returnStdout: true)
                }
                def buildProps = readProperties file: workspace + '/GITWORK/' + gitRepo + '/.buildenv'
                withEnv(["GITBRANCH=" + gitBranch,
                        "ORG=" + gitOrg,
                        "GIT_REPO=" + gitRepo,
                        "ZRDDS_TAG=" + buildProps['ZRDDS_TAG'],
                        "ARCH=" + buildArch,
                        "AF_VER=" + buildProps['AF_VER'],
                        "FALLBACK_BRANCH=" + fallbackBranch,
                        "BUILD_DATE=" + buildDate,
                        "BUILD_ID=" + buildId
                ]) {
                    stage("Process ZRDDS Docker GitHub repo on ${interp}") {
                        try {
                            // Process all required GitHub projects
                            sh '''
                                ROOT=${PWD}
                                REPFILE=GIT_REPORT.txt
                                cd ${ROOT}/GITWORK
                                for REPO in ${GIT_REPO}
                                do
                                  REPO_U=`echo ${REPO} | tr "-" "_"`
                                  echo "export ${REPO_U}_repo=${REPO}" >> git.props
                                  cd ${REPO}
                                  COMMIT_HASH=`git rev-parse --verify ${GITBRANCH}`
                                  cd -
                                  echo "export ${REPO_U}_commit=${COMMIT_HASH}" >> git.props
                                done
                                find -name "*.sh" | xargs chmod 755
                            '''
                        }
                        catch (ignored) {
                            error(message: "'${STAGE_NAME}' failed.")
                            currentBuild.result = 'FAILURE'
                        }
                    }
                    stage("Create OCI images on ${interp}") {
                        try {
                            withCredentials([
                              string(
                                credentialsId: 'zldazaadevfunc',
                                variable: 'IBMCLOUD_API_KEY'
                              )
                            ]) {
                                // Clean up any containers and images left behind from prior work
                                sh '''
                                    docker rm -vf $(docker ps -a -q) || docker ps -a
                                    docker rmi -f $(docker images -a -q) || docker images -a
                                '''
                                // For Linux on Z, update architecture suffixes for OCI image names
                                sh '''
                                    if [ "${ARCH}" = "s390x" ]
                                    then
                                        echo "Building for s390x; need to update architecture suffixes for OCI image names."
                                        cd GITWORK/${GIT_REPO}
                                        for DFILE in `find -name Dockerfile*`
                                        do
                                          sed -i -e "s%-x86_64$%-s390x%g" ${DFILE}
                                          sed -i -e "s%-x86_64\\ AS%-s390x\\ AS%g" ${DFILE}
                                          sed -i -e "s%/x86_64/%/s390x/%g" ${DFILE}
                                          sed -i -e "s%-amd64$%-s390x%g" ${DFILE}
                                          sed -i -e "s%-amd64\\ AS%-s390x\\ AS%g" ${DFILE}
                                          sed -i -e "s%/amd64/%/s390x/%g" ${DFILE}
                                        done
                                        for DCFILE in *docker-compose*yml
                                        do
                                          sed -i -e "s%-x86_64$%-s390x%g" ${DCFILE}
                                          sed -i -e "s%-amd64$%-s390x%g" ${DCFILE}
                                        done
                                    else
                                        echo "Building for x86_64; no Dockerfile customization required."
                                    fi
                                '''
                                // Navigate into master project and launch docker compose build process
                                sh '''
                                    export PATH=/usr/local/bin:/usr/local/sbin:${PATH}
                                    cd GITWORK
                                    ibmcloud login -r us-east
                                    ibmcloud cr login --client docker
                                    . ./git.props
                                    cd ${GIT_REPO}
                                    export ZRDDS_TAG
                                    if [ "${ARCH}" = "x86_64" ]
                                    then
                                      export ALT_ARCH=${ARCH}
                                      export OPENJDK_ARCH="x64"
                                      export NODE_ARCH=${OPENJDK_ARCH}
                                      export UBU_ARCH="amd64"
                                    elif [ "${ARCH}" = "arm64" ]
                                    then
                                      export ALT_ARCH="aarch64"
                                      export OPENJDK_ARCH=${ALT_ARCH}
                                      export NODE_ARCH=${ARCH}
                                    else
                                      export ALT_ARCH=${ARCH}
                                      export OPENJDK_ARCH=${ARCH}
                                      export NODE_ARCH=${ARCH}
                                      export UBU_ARCH=${ARCH}
                                    fi
                                    sed -i -e "s%^ZRDDS_TAG=.*$%ZRDDS_TAG=${ZRDDS_TAG}%g" ./.zoa_factory.config
                                    echo "Creating base images..."
                                    set -a
                                    . ./.zoa_factory.config
                                    set +a
                                    echo "Puling existing product images..."
                                    for IMG in zrdds-postgresql zrdds-orientdb zrdds-core zrdds-api zrdds-kafkaconnect
                                    do
                                      docker pull icr.io/zoa-oci/${IMG}:${ZRDDS_TAG}-${UBU_ARCH}
                                    done
                                    echo "Creating other product images..."
                                    docker compose --env-file zoa_env.config -f docker-compose-dev.yml build
                                    echo ""
                                '''
                            }
                        }
                        catch (ignored) {
                            error(message: "'${STAGE_NAME}' failed.")
                            currentBuild.result = 'FAILURE'
                        }
                    }
                    stage("Create OCI transportable images on ${interp}") {
                        try {
                            // Run 'docker save' against the generated images
                            sh '''
                                ROOT=${PWD}
                                cd GITWORK/${GIT_REPO}
                                export ZRDDS_TAG
                                if [ "${ARCH}" = "x86_64" ]
                                then
                                  export ALT_ARCH=${ARCH}
                                  export OPENJDK_ARCH="x64"
                                  export NODE_ARCH=${OPENJDK_ARCH}
                                  export UBU_ARCH="amd64"
                                elif [ "${ARCH}" = "arm64" ]
                                then
                                  export ALT_ARCH="aarch64"
                                  export OPENJDK_ARCH=${ALT_ARCH}
                                  export NODE_ARCH=${ARCH}
                                else
                                  export ALT_ARCH=${ARCH}
                                  export OPENJDK_ARCH=${ARCH}
                                  export NODE_ARCH=${ARCH}
                                  export UBU_ARCH=${ARCH}
                                fi
                                IMGSTRING=""
                                for IMG in zrdds-postgresql zrdds-orientdb zrdds-core zrdds-api zrdds-kafkaconnect zrdds-kafkabridge
                                do
                                  FULLIMG=icr.io/zoa-oci/${IMG}:${ZRDDS_TAG}-${UBU_ARCH}
                                  IMGSTRING="${IMGSTRING} ${FULLIMG}"
                                done
                                docker save -o ${ROOT}/DIST/zrdds_images.tar ${IMGSTRING}
                            '''
                            // Package the individual TAR files, plus supporting scripts, into a gzipped TAR file
                            sh '''
                                ROOT=${PWD}
                                mkdir -p ${ROOT}/DIST/utils ${ROOT}/DIST/bin/utils ${ROOT}/DIST/samples/ztopology
                                cd ${ROOT}/GITWORK/${GIT_REPO}
                                cp zoa_env.config ${ROOT}/DIST/
                                cp .zoa_factory.config ${ROOT}/DIST/
                                cp zrdds-docker-compose.yml ${ROOT}/DIST/
                                cp tools/*.sh ${ROOT}/DIST/utils && chmod 755 ${ROOT}/DIST/utils/*.sh
                                cp tools/.featurespec ${ROOT}/DIST/
                                cp tools/*.rsp ${ROOT}/DIST/
                                cp config/ztopology/ztopology-config.json ${ROOT}/DIST/samples/ztopology/
                                for FILE in `find ${ROOT}/DIST -name "*.sh"`
                                do
                                  sed -i -e "s%__IMGFILE__%ZRDDS-\\${ARCH}.tar.gz%g" ${FILE}
                                  sed -i -e "s%__PRODVER__%${ZRDDS_TAG}%g" ${FILE}
                                  sed -i -e "s%__BUILDID__%${BUILD_ID}%g" ${FILE}
                                  sed -i -e "s%__BUILDDATE__%${BUILD_DATE}%g" ${FILE}
                                done
                                mv ${ROOT}/DIST/utils/*Manage-zrdds.sh ${ROOT}/DIST/bin/utils
                                mv ${ROOT}/DIST/utils/zrdds-common.sh ${ROOT}/DIST/bin/utils
                                mv ${ROOT}/DIST/utils/zrdds-extend-field-length.sh ${ROOT}/DIST/bin
                                mv ${ROOT}/DIST/utils/zrdds-addcron.sh ${ROOT}/DIST/bin
                                cd ${ROOT}/DIST
                                tar cvzf ZRDDS-${ARCH}.tar.gz --numeric-owner --owner=0 --group=0 *.tar *.yml zoa_env.config .zoa_factory.config bin/ samples/ && rm -Rf *.tar *.yml zoa_env.config .zoa_factory.config bin/ samples/
                                tar cvf ZRDDS-${ARCH}.tar --numeric-owner --owner=0 --group=0 *.tar.gz utils/ *.rsp .featurespec && rm -Rf *.tar.gz utils/ *.rsp .featurespec
                            '''
                        }
                        catch (ignored) {
                            error(message: "'${STAGE_NAME}' failed.")
                            currentBuild.result = 'FAILURE'
                        }
                    }
                    stage("Publish OCI artifacts for ${interp}") {
                        try {
                           // Publish mega-TAR file to Artifactory
                           def uploadSpec = """{
                           "files": [
                             {
                               "pattern": "DIST/ZRDDS-${ARCH}.tar",
                               "target": "sys-izoa-zds-generic-local/OCI/${AF_VER}/${GITBRANCH}/${ARCH}/",
                               "flat": "true"
                             }
                           ]
                           }""" 
                           jfServer.upload spec: uploadSpec
                           // Publish OCI images to Artifactory
                           sh '''
                             export ZRDDS_TAG
                             if [ "${ARCH}" = "x86_64" ]
                             then
                               export ALT_ARCH=${ARCH}
                               export OPENJDK_ARCH="x64"
                               export NODE_ARCH=${OPENJDK_ARCH}
                               export UBU_ARCH="amd64"
                             elif [ "${ARCH}" = "arm64" ]
                             then
                               export ALT_ARCH="aarch64"
                               export OPENJDK_ARCH=${ALT_ARCH}
                               export NODE_ARCH=${ARCH}
                             else
                               export ALT_ARCH=${ARCH}
                               export OPENJDK_ARCH=${ARCH}
                               export NODE_ARCH=${ARCH}
                               export UBU_ARCH=${ARCH}
                             fi
                             for IMG in zrdds-kafkabridge
                             do
                               docker push icr.io/zoa-oci/${IMG}:${ZRDDS_TAG}-${UBU_ARCH}
                             done
                           '''
                        }
                        catch (ignored) {
                            error(message: "'${STAGE_NAME}' failed.")
                            currentBuild.result = 'FAILURE'
                        }
                    }
                }
            // }
        }
    }
}

parallel branches

class StageDef implements Serializable {

    String interp

    StageDef(final String interp) {
        this.interp = interp
    }
}
