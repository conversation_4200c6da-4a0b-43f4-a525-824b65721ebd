################################################################################
## DO NOT MODIFY THE SETTINGS IN THIS FILE UNLESS INSTRUCTED BY IBM SUPPORT #
################################################################################
#
################################################################################
## Z RESOURCE DISCOVERY DATA SERVICE SETTINGS #
################################################################################

# DBZ_TOKEN has the format <user_id>:<password>, base64-encoded
ZRDDS_API_HOST=zrdds-api
ZRDDS_API_LOGGING_LEVEL=
ZRDDS_CORE_HOST=zrdds-core
ZRDDS_PORT=8888
ZRDDS_CORE_LOGGING_LEVEL=
# PGSQL_TOKEN has the format <user_id>:<password>, base64-encoded
PGSQL_TOKEN=ZGJ6dXNlcjpkYnotcGFzc3dvcmQ=
# PGSQL_ADMIN_TOKEN has the format <user_id>:<password>, base64-encoded
PGSQL_ADMIN_TOKEN=cG9zdGdyZXM6RGlzY292ZXJ5NHBvc3RncmVz
PGSQL_PORT=5432
PGSQL_DB=discovery
COREAPI_PORT=8080
ORIENTDB_PORT=2480
# ORIENT_TOKEN has the format<user_id>:<password>, base64-encoded
ORIENT_TOKEN=cm9vdDpyb290cHdk
ZRDDS_TAG=1.4.3
# Kafka Bridge
ZRDDS_KB_HOST=zrdds-kafkabridge