################################################################################
# SETTINGS FOR SERVICENOW INTEGRATION SERVICES                                 #
################################################################################

# ServiceNow CMDB integration settings
#-------------------------------------------------------------------------------
# ROUTECARD_FILE_STORE=<Directory in which JCL routecard files are stored for load into ZRDDS>
#
# Description:
#   This value specifies the file store (directory) in which routecard files are
#   stored prior to loading them into the Z Resource Discovery Data Service.
#   Routecards are required by the JCL processing capability to map JCL to the
#   LPAR in which it runs.
#
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   ROUTECARD_FILE_STORE=/usr/local/share/rcfs
#
# Notes:
#
#-------------------------------------------------------------------------------
ROUTECARD_FILE_STORE=

#-------------------------------------------------------------------------------
# DISCOVERY_AGENT_COUNT=<Number of discovery agents configured to send data to the gateway service>
#
# Description:
#   This property specifies how many discovery agents are configured to send
#   data to the Z Operational Analytics gateway service at the same time.
#   To prevent Denial of Service attacks, each discovery agent will be allotted 
#   a specific request rate (see ZAIOPS_GATEWAY_REQUESTS_PER_MIN).
#
# Required value? No
#
# Default value: 0
#
# Example:
#   DISCOVERY_AGENT_COUNT=10
#
# Notes:
#
#-------------------------------------------------------------------------------
DISCOVERY_AGENT_COUNT=

#-------------------------------------------------------------------------------
# ORIENTDB_HEAP_SIZE=<Java heap size for OrientDB database embedded in ZRDDS service>
#
# Description:
#   This value specifies the heap size for the OrientDB database embedded in the
#   Z Resource Discovery Data Service component. You must specify both minimum
#   and maximum heap size as standard JVM parameters, and both values must be 
#   the same.
#
# Required value? No
#
# Default value: This property has no default value
#
# Example:
#   ORIENTDB_HEAP_SIZE="-Xms2G -Xmx2G"
#
# Notes:
#
#-------------------------------------------------------------------------------
ORIENTDB_HEAP_SIZE="-Xms2G -Xmx2G"

#-------------------------------------------------------------------------------
# DISCOVERY_LIBRARY_FILE_STORE=<Directory into which z/OS DLA IdML books get transferred>
#
# Description:
#   This value specifies the file store (directory) configured in the z/OS Discovery
#   Library Adapter FTP configuration member IZDCFTPI. This directory is the destination
#   in which the FTP transfer from z/OS Discovery Library Adapter to Discovery Library
#   File Store will be stored for consumption by components such as the Z Resource
#   Discovery Data Service.
#
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   DISCOVERY_LIBRARY_FILE_STORE=/usr/local/share/dlfs
#
# Notes:
#
#-------------------------------------------------------------------------------
DISCOVERY_LIBRARY_FILE_STORE=

#-------------------------------------------------------------------------------
# DISCOVERY_SCENARIO=<Which Z Discovery use case to support>
#
# Description:
#   This value specifies which of the supported Z Discovery integration scenarios
#   should be enabled.
#   Valid options are:
#   - NO_SNOW: disable ServiceNow CMDB integration but enable all other use cases
#     The following software containers will be disabled:
#     - zrdds-kafkaconnect
#     - zrdds-kafkabridge
#   - ALL: enable all integration use cases
#     No software containers will be disabled
#
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   DISCOVERY_SCENARIO=SNOW_ONLY
#
#
#-------------------------------------------------------------------------------
DISCOVERY_SCENARIO=ALL
