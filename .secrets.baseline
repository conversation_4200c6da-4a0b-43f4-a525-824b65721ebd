{"exclude": {"files": "^.secrets.baseline$", "lines": null}, "generated_at": "2024-07-26T07:20:06Z", "plugins_used": [{"name": "AWSKeyDetector"}, {"name": "ArtifactoryDetector"}, {"name": "AzureStorageKeyDetector"}, {"base64_limit": 4.5, "name": "Base64HighEntropyString"}, {"name": "BasicAuthDetector"}, {"name": "BoxDetector"}, {"name": "CloudantDetector"}, {"ghe_instance": "github.ibm.com", "name": "GheDetector"}, {"name": "GitHubTokenDetector"}, {"hex_limit": 3, "name": "HexHighEntropyString"}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "JwtTokenDetector"}, {"keyword_exclude": null, "name": "KeywordDetector"}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "PrivateKeyDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TwilioKeyDetector"}], "results": {"src/main/java/com/ibm/palantir/catelyn/util/HttpUtils.java": [{"hashed_secret": "fe010f73fb8ad3cad0718f4f4e39fa83024b1310", "is_secret": false, "is_verified": false, "line_number": 457, "type": "Base64 High Entropy String", "verified_result": null}]}, "version": "0.13.1+ibm.62.dss", "word_list": {"file": null, "hash": null}}