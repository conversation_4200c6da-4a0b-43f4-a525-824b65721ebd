FROM icr.io/zoa-oci/zoacommon-jre21-micro:21.0.5_11-x86_64

ARG TINI_VER=0.19.0
ARG REPO=unknown
ARG COMMIT=unknown

LABEL feature="IBM Z AIOps - Z Resource Discovery Data Service"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

RUN curl -s -O -L https://github.com/strimzi/strimzi-kafka-bridge/releases/download/0.31.2/kafka-bridge-0.31.2.tar.gz \
    && tar xzf /kafka-bridge-0.31.2.tar.gz \
    && rm -f /kafka-bridge-0.31.2.tar.gz \
    && mv /kafka-bridge-0.31.2 /opt/kafka-bridge

COPY zrdds-zoa-support/kafka/kafkabridge/config/application.properties /opt/kafka-bridge/config/
RUN chmod 0660 /opt/kafka-bridge/config/application.properties

ENV TINI_VERSION v0.19.0
ENV TINI_SHA256_AMD64=93dcc18adc78c65a028a84799ecf8ad40c936fdfc5f2a57b1acda5a8117fa82c
ENV TINI_SHA256_ARM64=07952557df20bfd2a95f9bef198b445e006171969499a1d361bd9e6f8e5e0e81
ENV TINI_SHA256_PPC64LE=3f658420974768e40810001a038c29d003728c5fe86da211cff5059e48cfdfde
ENV TINI_SHA256_S390X=931b70a182af879ca249ae9de87ef68423121b38d235c78997fafc680ceab32d

RUN set -ex; \
    TINI_BIN="" ; \
    case "$(arch)" in \
    x86_64)  TINI_BIN='tini-amd64' ;; \
    s390x)   TINI_BIN='tini-s390x' ;; \
    aarch64)   TINI_BIN='tini-arm64' ;; \
    *) echo >&2 ; echo >&2 "Unsupported architecture $(arch)" ; echo >&2 ; exit 1 ;; \
    esac ; \
    curl --retry 8 -S -L -O https://github.com/krallin/tini/releases/download/v${TINI_VER}/${TINI_BIN} ; \
    curl --retry 8 -S -L -O https://github.com/krallin/tini/releases/download/v${TINI_VER}/${TINI_BIN}.sha256sum ; \
    sha256sum -c ${TINI_BIN}.sha256sum ; \
    rm ${TINI_BIN}.sha256sum ; \
    mv ${TINI_BIN} /usr/bin/tini ; \
    chmod +x /usr/bin/tini

COPY zrdds-zoa-support/kafka/kafkabridge/bin/docker-entrypoint.sh zrdds-zoa-support/kafka/kafkabridge/bin/healthcheck.sh /usr/local/bin/

RUN groupadd --gid=1000 strimzi && \
    useradd --uid 1000 --gid 1000 strimzi && \
    chown -R strimzi:strimzi /opt/kafka-bridge

RUN chmod g=u /etc/passwd && \
    chmod 0775 /usr/local/bin/docker-entrypoint.sh /usr/local/bin/healthcheck.sh

USER 1000

HEALTHCHECK NONE
# HEALTHCHECK CMD /usr/local/bin/healthcheck.sh

ENTRYPOINT ["/usr/bin/tini", "--", "/usr/local/bin/docker-entrypoint.sh"]
