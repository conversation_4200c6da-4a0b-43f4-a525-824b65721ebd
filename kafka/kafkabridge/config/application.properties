#Bridge related settings
bridge.id=zoa-bridge
# uncomment the following line (bridge.tracing) to enable OpenTelemetry tracing, check the documentation for more details
#bridge.tracing=opentelemetry

#Apache Kafka common
kafka.bootstrap.servers=kafkabroker:19092

#Apache Kafka producer
kafka.producer.acks=1

#Apache Kafka consumer
kafka.consumer.auto.offset.reset=latest

#HTTP related settings
http.host=0.0.0.0
http.port=8081
#Enable CORS
http.cors.enabled=false
http.cors.allowedOrigins=*
http.cors.allowedMethods=GET,POST,PUT,DELETE,OPTIONS,PATCH

#Enable consumer
http.consumer.enabled=true

#Enable producer
http.producer.enabled=true
